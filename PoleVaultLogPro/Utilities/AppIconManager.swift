import UIKit
import SwiftUI

enum AppIconType: String, CaseIterable {
    case male = "AppIcon"
    case female = "AppIconFemale"

    var displayName: String {
        switch self {
        case .male: return "Male Icon"
        case .female: return "Female Icon"
        }
    }

    var isPrimary: Bool {
        return self == .male
    }

    var themeColor: String {
        switch self {
        case .male: return "Blue"
        case .female: return "Pink"
        }
    }

    var iconPreviewName: String {
        switch self {
        case .male: return "MaleJumperIcon"
        case .female: return "FemaleJumperIcon"
        }
    }
}

class AppIconManager {
    static let shared = AppIconManager()

    private init() {}

    /// Check if the device supports alternate icons
    var supportsAlternateIcons: Bool {
        return UIApplication.shared.supportsAlternateIcons
    }

    /// Change the app icon
    func changeAppIcon(to iconType: AppIconType) {
        // If selecting the primary icon, pass nil to reset to the primary icon
        let iconName = iconType.isPrimary ? nil : iconType.rawValue

        // Only try to change the icon if the device supports it
        if supportsAlternateIcons {
            UIApplication.shared.setAlternateIconName(iconName) { error in
                if let error = error {
                    print("Error changing app icon: \(error.localizedDescription)")
                } else {
                    print("App icon changed successfully to \(iconType.rawValue)")
                    // Save the selected icon preference
                    UserDefaults.standard.set(iconType.rawValue, forKey: "selectedAppIcon")

                    // Notify that theme has changed
                    NotificationCenter.default.post(name: NSNotification.Name("AppThemeChanged"), object: nil)
                }
            }
        } else {
            // Even if we can't change the icon, still save the preference for theming
            UserDefaults.standard.set(iconType.rawValue, forKey: "selectedAppIcon")

            // Notify that theme has changed
            NotificationCenter.default.post(name: NSNotification.Name("AppThemeChanged"), object: nil)
        }
    }

    /// Change just the theme without changing the app icon
    func changeTheme(to iconType: AppIconType) {
        // Save the selected theme preference
        UserDefaults.standard.set(iconType.rawValue, forKey: "selectedAppIcon")

        // Notify that theme has changed
        NotificationCenter.default.post(name: NSNotification.Name("AppThemeChanged"), object: nil)
    }

    /// Get the current app icon/theme
    func getCurrentTheme() -> AppIconType {
        let savedTheme = UserDefaults.standard.string(forKey: "selectedAppIcon") ?? AppIconType.male.rawValue
        return AppIconType(rawValue: savedTheme) ?? .male
    }
}
