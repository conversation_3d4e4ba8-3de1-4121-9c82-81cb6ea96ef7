import Foundation

/// Centralized configuration for the app
struct AppConfiguration {
    /// The CloudKit container identifier used for iCloud sync
    static var cloudKitContainerID: String {
        // Always use the consistent CloudKit container ID
        let containerID = "iCloud.com.brummblebee.pvlogpro"
        print("Using CloudKit container ID: \(containerID)")
        return containerID
    }

    /// The app's bundle identifier
    static var bundleIdentifier: String {
        let bundleId = Bundle.main.bundleIdentifier ?? "com.brummblebee.polevaultlogpro"
        print("App bundle identifier: \(bundleId)")
        return bundleId
    }

    /// The Core Data model name
    static let coreDataModelName = "VaultLog"

    /// App display name
    static var appDisplayName: String {
        return Bundle.main.object(forInfoDictionaryKey: "CFBundleDisplayName") as? String ?? "PoleVault Log Pro"
    }

    /// Print all configuration values for debugging
    static func printDebugInfo() {
        print("=== App Configuration Debug Info ===")
        print("Bundle ID: \(bundleIdentifier) (lowercase)")
        print("CloudKit Container ID: \(cloudKitContainerID)")
        print("Core Data Model Name: \(coreDataModelName)")
        print("App Display Name: \(appDisplayName)")
        print("===================================")

        // Important note about the relationship between bundle ID and CloudKit container ID
        print("NOTE: The bundle ID and CloudKit container ID must be registered in the Apple Developer Portal.")
        print("The CloudKit container ID must be 'iCloud.com.brummblebee.pvlogpro'")
        print("The bundle ID must be 'com.brummblebee.polevaultlogpro' (all lowercase)")
    }
}
