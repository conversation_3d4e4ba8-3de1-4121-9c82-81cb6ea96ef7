import Foundation
import CoreData
import CloudKit

/// A minimal CloudKit sync manager that observes NSPersistentCloudKitContainer events
/// This class exists primarily for backward compatibility with existing code
class CloudKitSyncManager {
    // MARK: - Singleton

    /// Shared instance
    static let shared = CloudKitSyncManager()

    // MARK: - Properties

    /// Last sync time
    private(set) var lastSyncTime: Date?

    /// Notification names for CloudKit sync events
    enum NotificationName {
        static let cloudKitDataChanged = NSNotification.Name("CloudKitDataChanged")
        static let manualSyncCompleted = NSNotification.Name("ManualCloudKitSyncCompleted")
        static let manualSyncFailed = NSNotification.Name("ManualCloudKitSyncFailed")
    }

    // MARK: - Initialization

    private init() {
        print("🔄 CloudKitSyncManager: Initialized in minimal mode")

        // Set up observer for NSPersistentCloudKitContainer events
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleCloudKitEvent(_:)),
            name: NSPersistentCloudKitContainer.eventChangedNotification,
            object: nil
        )

        // Set up observer for manual sync completion
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleManualSyncCompleted),
            name: NotificationName.manualSyncCompleted,
            object: nil
        )

        // Set up observer for manual sync failure
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleManualSyncFailed(_:)),
            name: NotificationName.manualSyncFailed,
            object: nil
        )
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }

    // MARK: - Event Handling

    @objc private func handleCloudKitEvent(_ notification: Notification) {
        guard let event = notification.userInfo?[NSPersistentCloudKitContainer.eventNotificationUserInfoKey]
            as? NSPersistentCloudKitContainer.Event else {
            return
        }

        // Update last sync time and notify observers
        lastSyncTime = Date()

        switch event.type {
        case .import:
            print("✅ CloudKit import event: \(event)")
            NotificationCenter.default.post(name: NotificationName.cloudKitDataChanged, object: nil)
        case .export:
            print("✅ CloudKit export event: \(event)")
            NotificationCenter.default.post(name: NotificationName.cloudKitDataChanged, object: nil)
        default:
            break
        }
    }

    /// Handles manual sync completion notifications
    @objc private func handleManualSyncCompleted() {
        print("✅ Manual CloudKit sync completed successfully")

        // Update last sync time
        lastSyncTime = Date()

        // Notify observers that data has changed
        NotificationCenter.default.post(name: NotificationName.cloudKitDataChanged, object: nil)
    }

    /// Handles manual sync failure notifications
    @objc private func handleManualSyncFailed(_ notification: Notification) {
        if let error = notification.userInfo?["error"] as? Error {
            print("❌ Manual CloudKit sync failed: \(error.localizedDescription)")

            // Handle specific error types if needed
            if let ckError = error as? CKError {
                switch ckError.code {
                case .networkUnavailable, .networkFailure:
                    print("Network error during manual sync: \(ckError.localizedDescription)")
                case .quotaExceeded:
                    print("CloudKit quota exceeded during manual sync")
                case .serverRejectedRequest:
                    print("CloudKit server rejected request during manual sync")
                default:
                    print("Other CloudKit error during manual sync: \(ckError.localizedDescription)")
                }
            }
        } else {
            print("❌ Manual CloudKit sync failed with unknown error")
        }
    }
}


