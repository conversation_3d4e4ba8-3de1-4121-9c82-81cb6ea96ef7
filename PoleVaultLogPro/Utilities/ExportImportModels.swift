import Foundation
import CoreData

/// Models for exporting and importing data in JSON format
/// These models conform to Codable for easy serialization/deserialization

// MARK: - Export Models

/// Represents an athlete for export/import
struct AthleteExport: Codable {
    let id: String
    let name: String
    let dateOfBirth: Date?
    let dominantHand: String?
    let personalBestCm: Double
    let personalBestIn: Double
    let poles: [PoleExport]

    /// Creates an export model from a Core Data Athlete entity
    init(from athlete: Athlete) {
        self.id = athlete.id ?? UUID().uuidString
        self.name = athlete.name ?? "Unknown Athlete"
        self.dateOfBirth = athlete.dateOfBirth
        self.dominantHand = athlete.dominantHand
        self.personalBestCm = athlete.personalBestCm
        self.personalBestIn = athlete.personalBestIn

        // Convert poles
        if let poles = athlete.poles?.allObjects as? [Pole] {
            self.poles = poles.sorted(by: { $0.order < $1.order }).map { PoleExport(from: $0) }
        } else {
            self.poles = []
        }
    }
}

/// Represents a pole for export/import
struct PoleExport: Codable {
    let id: String
    let name: String
    let brand: String?
    let weight: Double
    let lengthCm: Double
    let lengthIn: Double
    let color: String?
    let order: Int16

    /// Creates an export model from a Core Data Pole entity
    init(from pole: Pole) {
        self.id = pole.id ?? UUID().uuidString
        self.name = pole.name ?? "Unknown Pole"
        self.brand = pole.brand
        self.weight = pole.weight
        self.lengthCm = pole.lengthCm
        self.lengthIn = pole.lengthIn
        self.color = pole.color
        self.order = pole.order
    }
}

/// Represents a jump for export/import
struct JumpExport: Codable {
    let id: String
    let order: Int16
    let columnIndex: Int16
    let attemptIndex: Int16
    let barHeightCm: Double
    let barHeightIn: Double
    let result: String
    let resultCode: String
    let comment: String?
    let runStartCm: Double
    let handHoldCm: Double
    let takeOffStepCm: Double
    let standardCm: Double
    let useBar: Bool
    let poleId: String?

    /// Creates an export model from a Core Data Jump entity
    init(from jump: Jump) {
        self.id = jump.id ?? UUID().uuidString
        self.order = jump.order
        self.columnIndex = jump.columnIndex
        self.attemptIndex = jump.attemptIndex
        self.barHeightCm = jump.barHeightCm
        self.barHeightIn = jump.barHeightIn
        self.result = jump.result ?? "miss"
        self.resultCode = jump.resultCode ?? "X"
        self.comment = jump.comment
        self.runStartCm = jump.runStartCm
        self.handHoldCm = jump.handHoldCm
        self.takeOffStepCm = jump.takeOffStepCm
        self.standardCm = jump.standardCm
        self.useBar = jump.useBar
        self.poleId = jump.pole?.id
    }
}

/// Represents a session for export/import
struct SessionExport: Codable {
    let id: String
    let date: Date
    let type: String
    let title: String
    let location: String?
    let weather: String?
    let heightStandard: String
    let notesAthlete: String?
    let notesCoach: String?
    let bestHeightCm: Double
    let bestHeightIn: Double
    let gridHeightsCm: [Double]
    let jumps: [JumpExport]
    let athleteId: String

    /// Creates an export model from a Core Data Session entity
    init(from session: Session) {
        self.id = session.id ?? UUID().uuidString
        self.date = Date(timeIntervalSinceReferenceDate: session.date)
        self.type = session.type ?? "practice"
        self.title = session.title ?? "Session"
        self.location = session.location
        self.weather = session.weather
        self.heightStandard = session.heightStandard ?? "Free-Range"
        self.notesAthlete = session.notesAthlete
        self.notesCoach = session.notesCoach
        self.bestHeightCm = session.bestHeightCm
        self.bestHeightIn = session.bestHeightIn

        // Get grid heights from the relationship
        self.gridHeightsCm = session.getGridHeights()

        // Convert jumps
        if let jumps = session.jumps?.allObjects as? [Jump] {
            self.jumps = jumps.map { JumpExport(from: $0) }
        } else {
            self.jumps = []
        }

        // Get athlete ID
        self.athleteId = session.athlete?.id ?? ""
    }
}

/// Represents the full export data for all sessions
struct FullExport: Codable {
    let appVersion: String
    let exportDate: Date
    let athletes: [AthleteExport]
    let sessions: [SessionExport]

    /// Creates a full export model from Core Data entities
    init(athletes: [Athlete], sessions: [Session]) {
        // Get app version
        let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0"

        self.appVersion = appVersion
        self.exportDate = Date()
        self.athletes = athletes.map { AthleteExport(from: $0) }
        self.sessions = sessions.map { SessionExport(from: $0) }
    }
}

// MARK: - Import Models

extension AthleteExport {
    /// Creates a Core Data Athlete entity from this export model
    func toEntity(in context: NSManagedObjectContext) -> Athlete {
        let athlete = Athlete(context: context)
        athlete.id = self.id
        athlete.name = self.name
        athlete.dateOfBirth = self.dateOfBirth
        athlete.dominantHand = self.dominantHand
        athlete.personalBestCm = self.personalBestCm
        athlete.personalBestIn = self.personalBestIn
        return athlete
    }
}

extension PoleExport {
    /// Creates a Core Data Pole entity from this export model
    func toEntity(in context: NSManagedObjectContext, athlete: Athlete) -> Pole {
        let pole = Pole(context: context)
        pole.id = self.id
        pole.name = self.name
        pole.brand = self.brand
        pole.weight = self.weight
        pole.lengthCm = self.lengthCm
        pole.lengthIn = self.lengthIn
        pole.color = self.color
        pole.order = self.order
        pole.athlete = athlete
        return pole
    }
}

extension JumpExport {
    /// Creates a Core Data Jump entity from this export model
    func toEntity(in context: NSManagedObjectContext, session: Session, poleMap: [String: Pole]? = nil) -> Jump {
        let jump = Jump(context: context)
        jump.id = self.id
        jump.order = self.order
        jump.columnIndex = self.columnIndex
        jump.attemptIndex = self.attemptIndex
        jump.barHeightCm = self.barHeightCm
        jump.barHeightIn = self.barHeightIn
        jump.result = self.result
        jump.resultCode = self.resultCode
        jump.comment = self.comment
        jump.runStartCm = self.runStartCm
        jump.handHoldCm = self.handHoldCm
        jump.takeOffStepCm = self.takeOffStepCm
        jump.standardCm = self.standardCm
        jump.useBar = self.useBar
        jump.session = session

        // Set pole if available
        if let poleId = self.poleId, let poleMap = poleMap, let pole = poleMap[poleId] {
            jump.pole = pole
        }

        return jump
    }
}

extension SessionExport {
    /// Creates a Core Data Session entity from this export model
    func toEntity(in context: NSManagedObjectContext, athlete: Athlete) -> Session {
        let session = Session(context: context)
        session.id = self.id
        session.date = self.date.timeIntervalSinceReferenceDate
        session.type = self.type
        session.title = self.title
        session.location = self.location
        session.weather = self.weather
        session.heightStandard = self.heightStandard
        session.notesAthlete = self.notesAthlete
        session.notesCoach = self.notesCoach
        session.bestHeightCm = self.bestHeightCm
        session.bestHeightIn = self.bestHeightIn

        // Create GridHeight entities for each height
        for (index, heightCm) in self.gridHeightsCm.enumerated() {
            _ = GridHeight.create(
                in: context,
                session: session,
                heightCm: heightCm,
                order: Int16(index)
            )
        }

        session.athlete = athlete

        // Don't create jumps here - they'll be created separately
        // to ensure proper relationships

        return session
    }
}
