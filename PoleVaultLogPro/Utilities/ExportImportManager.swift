import Foundation
import CoreData
import UIKit
import UniformTypeIdentifiers
import Photos<PERSON>

/// Manages the export and import of data
@available(iOS 14.0, *)
class ExportImportManager {
    /// Shared instance
    static let shared = ExportImportManager()

    /// File extension for our JSON export files
    static let jsonExtension = "pvl.json"

    /// File extension for CSV files
    static let csvExtension = "csv"

    /// File extension for ZIP files
    static let zipExtension = "zip"

    /// The UTType for our JSON export files
    @available(iOS 14.0, *)
    static var jsonUTType: UTType {
        return UTType.poleVaultLogProJSON
    }

    /// The UTType for CSV files
    @available(iOS 14.0, *)
    static var csvUTType: UTType {
        if let type = UTType(tag: "csv", tagClass: .filenameExtension, conformingTo: .text) {
            return type
        }
        return .text
    }

    /// The UTType for ZIP files
    @available(iOS 14.0, *)
    static var zipUTType: UTType {
        if let type = UTType(tag: "zip", tagClass: .filenameExtension, conformingTo: .archive) {
            return type
        }
        return .archive
    }

    /// Date formatter for file names
    private let dateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter
    }()

    /// Private initializer for singleton
    private init() {}

    // MARK: - Export Functions

    /// Exports a single session to JSON
    /// - Parameter session: The session to export
    /// - Returns: URL to the temporary JSON file
    func exportSessionToJSON(_ session: Session) throws -> URL {
        // Create the full export model with just this session
        let fullExport = FullExport(
            athletes: [session.athlete].compactMap { $0 },
            sessions: [session]
        )

        // Encode to JSON
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        encoder.outputFormatting = [.prettyPrinted, .sortedKeys]
        let jsonData = try encoder.encode(fullExport)

        // Create a temporary file
        let fileName = "Session-\(session.title ?? "Export")-\(dateFormatter.string(from: Date(timeIntervalSinceReferenceDate: session.date))).\(ExportImportManager.jsonExtension)"
        let fileURL = FileManager.default.temporaryDirectory.appendingPathComponent(fileName)

        // Write the JSON data to the file
        try jsonData.write(to: fileURL)

        return fileURL
    }

    /// Exports a single session to CSV format
    /// - Parameter session: The session to export
    /// - Returns: URL to the temporary CSV file
    func exportSessionToCSV(_ session: Session) throws -> URL {
        let csvData = createCSVForSession(session)

        // Create a temporary file
        let fileName = "Session-\(session.title ?? "Export")-\(dateFormatter.string(from: Date(timeIntervalSinceReferenceDate: session.date))).\(ExportImportManager.csvExtension)"
        let fileURL = FileManager.default.temporaryDirectory.appendingPathComponent(fileName)

        // Write the CSV data to the file
        try csvData.write(to: fileURL, atomically: true, encoding: .utf8)

        return fileURL
    }

    /// Exports all sessions to a JSON file
    /// - Parameters:
    ///   - athletes: All athletes to export
    ///   - sessions: All sessions to export
    /// - Returns: URL to the temporary JSON file
    func exportAllToJSON(athletes: [Athlete], sessions: [Session]) throws -> URL {
        // Create the full export model
        let fullExport = FullExport(athletes: athletes, sessions: sessions)

        // Encode to JSON
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        encoder.outputFormatting = [.prettyPrinted, .sortedKeys]
        let jsonData = try encoder.encode(fullExport)

        // Create the JSON file
        let jsonFileName = "PoleVaultLogPro-Export-\(dateFormatter.string(from: Date())).\(ExportImportManager.jsonExtension)"
        let jsonFileURL = FileManager.default.temporaryDirectory.appendingPathComponent(jsonFileName)

        // Write the JSON data to the file
        try jsonData.write(to: jsonFileURL)

        return jsonFileURL
    }

    /// Exports all sessions to a CSV file
    /// - Parameter sessions: All sessions to export
    /// - Returns: URL to the temporary CSV file
    func exportAllToCSV(sessions: [Session]) throws -> URL {
        let csvData = createCSVForAllSessions(sessions)

        // Create the CSV file
        let csvFileName = "PoleVaultLogPro-Export-\(dateFormatter.string(from: Date())).\(ExportImportManager.csvExtension)"
        let csvFileURL = FileManager.default.temporaryDirectory.appendingPathComponent(csvFileName)

        // Write the CSV data to the file
        try csvData.write(to: csvFileURL, atomically: true, encoding: .utf8)

        return csvFileURL
    }

    // MARK: - Import Functions

    /// Imports data from a JSON file
    /// - Parameters:
    ///   - url: The URL of the JSON file
    ///   - context: The managed object context to import into
    ///   - handleOrphanedSessions: Optional closure to handle sessions with missing athlete IDs
    /// - Returns: A tuple containing the number of athletes and sessions imported, duplicates found, and any orphaned sessions
    func importFromJSON(
        url: URL,
        context: NSManagedObjectContext,
        handleOrphanedSessions: (([SessionExport]) -> Void)? = nil
    ) throws -> (athletes: Int, sessions: Int, duplicateSessions: Int, orphanedSessions: [SessionExport]?) {
        // Read the JSON data
        let jsonData = try Data(contentsOf: url)

        // Decode the JSON
        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = .iso8601
        let fullExport = try decoder.decode(FullExport.self, from: jsonData)

        // Create a dictionary of existing athletes by ID
        let existingAthletesFetchRequest: NSFetchRequest<Athlete> = Athlete.fetchRequest()
        let existingAthletes = try context.fetch(existingAthletesFetchRequest)
        var athletesById = [String: Athlete]()
        for athlete in existingAthletes {
            if let id = athlete.id {
                athletesById[id] = athlete
            }
        }

        // Create a dictionary of existing sessions by ID
        let existingSessionsFetchRequest: NSFetchRequest<Session> = Session.fetchRequest()
        let existingSessions = try context.fetch(existingSessionsFetchRequest)
        var sessionsById = [String: Session]()
        for session in existingSessions {
            if let id = session.id {
                sessionsById[id] = session
            }
        }

        // Import athletes
        var importedAthletes = 0
        for athleteExport in fullExport.athletes {
            if let existingAthlete = athletesById[athleteExport.id] {
                // Update existing athlete
                existingAthlete.name = athleteExport.name
                existingAthlete.dateOfBirth = athleteExport.dateOfBirth
                existingAthlete.dominantHand = athleteExport.dominantHand
                existingAthlete.personalBestCm = athleteExport.personalBestCm
                existingAthlete.personalBestIn = athleteExport.personalBestIn

                athletesById[athleteExport.id] = existingAthlete
            } else {
                // Create new athlete
                let newAthlete = athleteExport.toEntity(in: context)
                athletesById[athleteExport.id] = newAthlete
                importedAthletes += 1
            }
        }

        // Collect sessions with missing athlete IDs
        var orphanedSessions = [SessionExport]()

        // Import sessions
        var importedSessions = 0
        var duplicateSessions = 0

        // Debug information
        print("Attempting to import \(fullExport.sessions.count) sessions")

        for sessionExport in fullExport.sessions {
            // Debug information
            print("Processing session: \(sessionExport.id) - \(sessionExport.title)")

            // Check if the session has a valid athlete ID
            if sessionExport.athleteId.isEmpty || athletesById[sessionExport.athleteId] == nil {
                // Add to orphaned sessions for later handling
                print("Session has no valid athlete ID: \(sessionExport.id) - \(sessionExport.title)")
                orphanedSessions.append(sessionExport)
                continue
            }

            // Get the athlete for this session
            let athlete = athletesById[sessionExport.athleteId]!
            print("Found athlete for session: \(athlete.name ?? "Unknown")")

            if let existingSession = sessionsById[sessionExport.id] {
                // This is a duplicate session (same ID)
                print("Found existing session with ID: \(sessionExport.id) - \(sessionExport.title)")
                duplicateSessions += 1

                // Let the user know we're updating the session
                print("Updating existing session with new data")

                // Update existing session
                existingSession.date = sessionExport.date.timeIntervalSinceReferenceDate
                existingSession.type = sessionExport.type
                existingSession.title = sessionExport.title
                existingSession.location = sessionExport.location
                existingSession.weather = sessionExport.weather
                existingSession.heightStandard = sessionExport.heightStandard
                existingSession.notesAthlete = sessionExport.notesAthlete
                existingSession.notesCoach = sessionExport.notesCoach
                existingSession.bestHeightCm = sessionExport.bestHeightCm
                existingSession.bestHeightIn = sessionExport.bestHeightIn
                // Handle grid heights
                // First, delete existing grid heights
                if let existingGridHeights = existingSession.gridHeights?.allObjects as? [GridHeight] {
                    for gridHeight in existingGridHeights {
                        context.delete(gridHeight)
                    }
                }

                // Create new grid heights
                for (index, heightCm) in sessionExport.gridHeightsCm.enumerated() {
                    _ = GridHeight.create(
                        in: context,
                        session: existingSession,
                        heightCm: heightCm,
                        order: Int16(index)
                    )
                }


                existingSession.athlete = athlete

                // Delete existing jumps
                if let existingJumps = existingSession.jumps?.allObjects as? [Jump] {
                    for jump in existingJumps {
                        context.delete(jump)
                    }
                }

                // Create new jumps
                for jumpExport in sessionExport.jumps {
                    _ = jumpExport.toEntity(in: context, session: existingSession)
                }

                sessionsById[sessionExport.id] = existingSession
            } else {
                // Create new session
                print("Creating new session: \(sessionExport.id) - \(sessionExport.title)")
                let newSession = sessionExport.toEntity(in: context, athlete: athlete)

                // Create jumps
                print("Creating \(sessionExport.jumps.count) jumps for session")
                for jumpExport in sessionExport.jumps {
                    _ = jumpExport.toEntity(in: context, session: newSession)
                }

                sessionsById[sessionExport.id] = newSession
                importedSessions += 1
                print("Successfully imported session: \(sessionExport.id) - \(sessionExport.title)")
            }
        }

        // Save the context
        try context.save()

        // If there are orphaned sessions and a handler is provided, call it
        if !orphanedSessions.isEmpty && handleOrphanedSessions != nil {
            handleOrphanedSessions?(orphanedSessions)
            return (athletes: importedAthletes, sessions: importedSessions, duplicateSessions: duplicateSessions, orphanedSessions: orphanedSessions)
        }

        return (athletes: importedAthletes, sessions: importedSessions, duplicateSessions: duplicateSessions, orphanedSessions: orphanedSessions.isEmpty ? nil : orphanedSessions)
    }

    /// Imports orphaned sessions with a specified athlete
    /// - Parameters:
    ///   - sessions: The orphaned sessions to import
    ///   - athlete: The athlete to associate with the sessions
    ///   - context: The managed object context to import into
    /// - Returns: The number of sessions imported
    func importOrphanedSessions(
        sessions: [SessionExport],
        athlete: Athlete,
        context: NSManagedObjectContext
    ) throws -> Int {
        print("importOrphanedSessions: Starting import of \(sessions.count) sessions with athlete: \(athlete.name ?? "Unknown")")
        // Create a dictionary of existing sessions by ID
        let existingSessionsFetchRequest: NSFetchRequest<Session> = Session.fetchRequest()
        let existingSessions = try context.fetch(existingSessionsFetchRequest)
        var sessionsById = [String: Session]()
        for session in existingSessions {
            if let id = session.id {
                sessionsById[id] = session
            }
        }

        // Import sessions
        var importedSessions = 0
        for sessionExport in sessions {
            print("Processing orphaned session: \(sessionExport.id) - \(sessionExport.title)")

            if let existingSession = sessionsById[sessionExport.id] {
                print("Found existing session with ID: \(sessionExport.id) - updating")
                // Update existing session
                existingSession.date = sessionExport.date.timeIntervalSinceReferenceDate
                existingSession.type = sessionExport.type
                existingSession.title = sessionExport.title
                existingSession.location = sessionExport.location
                existingSession.weather = sessionExport.weather
                existingSession.heightStandard = sessionExport.heightStandard
                existingSession.notesAthlete = sessionExport.notesAthlete
                existingSession.notesCoach = sessionExport.notesCoach
                existingSession.bestHeightCm = sessionExport.bestHeightCm
                existingSession.bestHeightIn = sessionExport.bestHeightIn
                // Handle grid heights
                // First, delete existing grid heights
                if let existingGridHeights = existingSession.gridHeights?.allObjects as? [GridHeight] {
                    for gridHeight in existingGridHeights {
                        context.delete(gridHeight)
                    }
                }

                // Create new grid heights
                for (index, heightCm) in sessionExport.gridHeightsCm.enumerated() {
                    _ = GridHeight.create(
                        in: context,
                        session: existingSession,
                        heightCm: heightCm,
                        order: Int16(index)
                    )
                }


                existingSession.athlete = athlete

                // Delete existing jumps
                if let existingJumps = existingSession.jumps?.allObjects as? [Jump] {
                    print("Deleting \(existingJumps.count) existing jumps")
                    for jump in existingJumps {
                        context.delete(jump)
                    }
                }

                // Create new jumps
                print("Creating \(sessionExport.jumps.count) jumps for session")
                for jumpExport in sessionExport.jumps {
                    _ = jumpExport.toEntity(in: context, session: existingSession)
                }

                // Count updates as imports for better user feedback
                importedSessions += 1
                print("Successfully updated existing session: \(sessionExport.id)")
            } else {
                print("Creating new session: \(sessionExport.id) - \(sessionExport.title)")
                // Create new session
                let newSession = sessionExport.toEntity(in: context, athlete: athlete)

                // Create jumps
                print("Creating \(sessionExport.jumps.count) jumps for session")
                for jumpExport in sessionExport.jumps {
                    _ = jumpExport.toEntity(in: context, session: newSession)
                }

                importedSessions += 1
                print("Successfully imported orphaned session: \(sessionExport.id)")
            }
        }

        // Save the context
        try context.save()

        return importedSessions
    }

    // MARK: - Helper Functions

    /// Creates a CSV string for a single session
    /// - Parameter session: The session to create CSV for
    /// - Returns: A CSV string
    private func createCSVForSession(_ session: Session) -> String {
        var csv = "Session: \(session.title ?? "Unknown")\n"
        csv += "Date: \(dateFormatter.string(from: Date(timeIntervalSinceReferenceDate: session.date)))\n"
        csv += "Type: \(session.type ?? "Unknown")\n"
        csv += "Location: \(session.location ?? "Unknown")\n"
        csv += "Weather: \(session.weather ?? "Unknown")\n"
        csv += "Athlete: \(session.athlete?.name ?? "Unknown")\n\n"

        // Add jumps table
        csv += "Height (cm),Height (in),Attempt,Result,Run Start (cm),Hand Hold (cm),Take-Off Step (cm),Standard (cm),Comment\n"

        if let jumps = session.jumps?.allObjects as? [Jump] {
            // Sort jumps by columnIndex and attemptIndex
            let sortedJumps = jumps.sorted { (j1, j2) -> Bool in
                if j1.columnIndex != j2.columnIndex {
                    return j1.columnIndex < j2.columnIndex
                }
                return j1.attemptIndex < j2.attemptIndex
            }

            for jump in sortedJumps {
                csv += "\(jump.barHeightCm),\(jump.barHeightIn),\(jump.attemptIndex),\(jump.resultCode ?? ""),\(jump.runStartCm),\(jump.handHoldCm),\(jump.takeOffStepCm),\(jump.standardCm),\"\(jump.comment ?? "")\"\n"
            }
        }

        return csv
    }

    /// Creates a CSV string for all sessions
    /// - Parameter sessions: The sessions to create CSV for
    /// - Returns: A CSV string
    private func createCSVForAllSessions(_ sessions: [Session]) -> String {
        var csv = "PoleVaultLogPro Export - \(dateFormatter.string(from: Date()))\n\n"

        // Sort sessions by date
        let sortedSessions = sessions.sorted { (s1, s2) -> Bool in
            return s1.date > s2.date
        }

        for session in sortedSessions {
            csv += createCSVForSession(session)
            csv += "\n\n"
        }

        return csv
    }
}
