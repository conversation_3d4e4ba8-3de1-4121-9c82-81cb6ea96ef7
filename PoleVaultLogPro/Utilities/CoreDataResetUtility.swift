//
//  CoreDataResetUtility.swift
//  PoleVaultLogPro
//
//  Created by <PERSON> on 5/14/25.
//

import Foundation
import CoreData
import CloudKit
import UIKit

class CoreDataResetUtility {

    /// Completely resets the Core Data store by deleting the SQLite files
    /// This is a drastic measure but can help resolve model version conflicts
    static func resetCoreDataStore() -> Bool {
        print("=== CORE DATA RESET UTILITY ===")
        print("Starting Core Data store reset process")

        // Set a flag to indicate we're resetting the store
        // This will be used by the app to avoid accessing Core Data during reset
        UserDefaults.standard.set(true, forKey: "isResettingCoreData")

        // We need to perform the reset on the main thread to ensure all contexts are properly cleaned up
        DispatchQueue.main.async {
            // Post a notification to inform all view controllers to release their Core Data references
            NotificationCenter.default.post(name: NSNotification.Name("PrepareForCoreDataReset"), object: nil)

            // Give a small delay to allow contexts to clean up
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                // Now proceed with the actual reset
                performActualReset()
            }
        }

        return true
    }

    /// Performs the actual Core Data store reset after preparation
    private static func performActualReset() {
        print("Performing actual Core Data reset")

        // Get the shared PersistenceController
        let persistenceController = PersistenceController.shared
        let persistentContainer = persistenceController.container

        // Reset the view context to release all objects
        persistentContainer.viewContext.reset()

        // Unload all NSManagedObjectModel instances to prevent Core Data model conflicts
        NSManagedObjectContext.mergeChanges(fromRemoteContextSave: [:], into: [])

        // Create a new background context for the reset operation
        let resetContext = persistentContainer.newBackgroundContext()
        resetContext.performAndWait {
            // First, attempt to remove the stores from the coordinator
            let coordinator = persistentContainer.persistentStoreCoordinator
            print("Removing stores from coordinator")

            // Get the URL for the persistent store
            guard let storeURL = persistentContainer.persistentStoreDescriptions.first?.url else {
                print("Error: Could not find persistent store URL")
                return
            }

            print("Core Data store URL: \(storeURL)")

            // Get the support files URLs
            let storeDirectory = storeURL.deletingLastPathComponent()
            let fileManager = FileManager.default

            do {
                // First, remove all stores from the coordinator
                var storeURLs: [URL] = []
                for store in coordinator.persistentStores {
                    if let url = store.url {
                        storeURLs.append(url)
                    }

                    // Remove the store
                    try coordinator.remove(store)
                    print("Successfully removed store from coordinator")
                }

                // Get all files in the directory
                let directoryContents = try fileManager.contentsOfDirectory(at: storeDirectory, includingPropertiesForKeys: nil, options: [])
                print("Found \(directoryContents.count) files in store directory")

                // Filter for Core Data and CloudKit related files
                let filesToDelete = directoryContents.filter { url in
                    let filename = url.lastPathComponent
                    return filename.contains("VaultLog") ||
                           filename.contains("cloudkit") ||
                           filename.contains(".sqlite") ||
                           filename.contains("CoreDataCloudKit") ||
                           filename.contains(".ckstorage")
                }

                print("Found \(filesToDelete.count) files to delete")

                // Delete each file
                for fileURL in filesToDelete {
                    try fileManager.removeItem(at: fileURL)
                    print("Deleted file: \(fileURL.lastPathComponent)")
                }

                // Force a CloudKit reset by setting a flag
                UserDefaults.standard.set(true, forKey: "cloudKitResetPerformed")

                // Reset the flag indicating we're done with the reset
                UserDefaults.standard.set(false, forKey: "isResettingCoreData")

                // Set a flag to indicate that the app was reset
                UserDefaults.standard.set(true, forKey: "appWasReset")

                // Set a flag to indicate that the app needs to be restarted
                UserDefaults.standard.set(true, forKey: "needsAppRestart")

                print("Core Data store reset completed successfully")
                print("===============================")

                // Post a notification that Core Data has been reset
                DispatchQueue.main.async {
                    NotificationCenter.default.post(name: NSNotification.Name("CoreDataResetCompleted"), object: nil)

                    // Show an alert to the user that they need to restart the app
                    let alertController = UIAlertController(
                        title: "⚠️ RESTART REQUIRED ⚠️",
                        message: "The app's data has been reset.\n\nIMPORTANT: You MUST completely close the app by swiping it up from the app switcher, then restart it for the changes to take effect.\n\nIf you don't restart, the app will crash when you try to use it.",
                        preferredStyle: .alert
                    )

                    alertController.addAction(UIAlertAction(title: "I'll Restart Now", style: .default, handler: nil))

                    // Present the alert
                    if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                       let rootViewController = windowScene.windows.first?.rootViewController {
                        rootViewController.present(alertController, animated: true, completion: nil)
                    }
                }
            } catch {
                print("Failed to reset Core Data store: \(error)")
                print("===============================")

                // Reset the flag even if we failed
                UserDefaults.standard.set(false, forKey: "isResettingCoreData")

                // Post a notification that Core Data reset failed
                DispatchQueue.main.async {
                    NotificationCenter.default.post(name: NSNotification.Name("CoreDataResetFailed"), object: nil)
                }
            }
        }
    }

    /// Resets the CloudKit sync state without deleting user data
    static func resetCloudKitSyncState() {
        print("=== CLOUDKIT SYNC RESET ===")
        print("Resetting CloudKit sync state")

        // Get the container ID
        let containerID = AppConfiguration.cloudKitContainerID
        print("CloudKit container ID: \(containerID)")

        // Get the CKContainer
        let container = CKContainer(identifier: containerID)

        // Reset the CloudKit container's cache
        container.accountStatus { status, error in
            if let error = error {
                print("Error checking CloudKit account status: \(error.localizedDescription)")
            } else if status == .available {
                print("CloudKit account is available, proceeding with reset")

                // Force a reset of the local Core Data store
                _ = resetCoreDataStore()

                // Set a flag to indicate that CloudKit sync state has been reset
                UserDefaults.standard.set(true, forKey: "cloudKitSyncReset")
                UserDefaults.standard.set(Date().timeIntervalSince1970, forKey: "lastCloudKitSyncReset")

                // Set a flag to indicate that the app was reset
                UserDefaults.standard.set(true, forKey: "appWasReset")

                print("CloudKit sync state reset completed")

                // Post a notification that CloudKit sync state has been reset
                DispatchQueue.main.async {
                    NotificationCenter.default.post(name: NSNotification.Name("CloudKitSyncResetCompleted"), object: nil)
                }
            } else {
                print("CloudKit account is not available (status: \(status)), cannot reset sync state")
            }
        }

        print("CloudKit sync reset initiated")
        print("===========================")
    }
}
