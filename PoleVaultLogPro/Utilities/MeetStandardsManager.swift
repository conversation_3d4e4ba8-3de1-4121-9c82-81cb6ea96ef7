import Foundation
import SwiftUI

/// Manages the height standards for pole vault meets
class MeetStandardsManager: ObservableObject {
    // MARK: - Properties

    /// Singleton instance
    static let shared = MeetStandardsManager()

    /// Published properties for SwiftUI binding
    @Published var customStandards: [MeetStandard] = []
    @Published var nfhsStandard: MeetStandard = MeetStandardsManager.nfhsStandard
    @AppStorage("defaultMeetStandard") private var defaultMeetStandard: String = "NFHS"

    /// Public getter for the default meet standard ID
    var defaultStandardId: String {
        return defaultMeetStandard
    }

    // MARK: - Initialization

    private init() {
        loadCustomStandards()
        loadNFHSConfig()
    }

    // MARK: - Built-in Standards

    /// NFHS (National Federation of State High School Associations) standard - dynamic heights based on starting height and increment
    static let nfhsStandard = MeetStandard(
        id: "NFHS",
        name: "NFHS",
        description: "National Federation of State High School Associations",
        isBuiltIn: true,
        heights: [],
        isDynamic: true,
        startingHeight: 244, // Default starting height (8 feet)
        increment: 15,       // Default increment (15 cm ≈ 6 inches)
        maxHeight: 700       // Maximum height (7 meters)
    )

    /// FHSAA (Florida High School Athletic Association) standard heights in centimeters
    static let fhsaaStandard = MeetStandard(
        id: "FHSAA",
        name: "FHSAA",
        description: "Florida High School Athletic Association",
        isBuiltIn: true,
        heights: [
            150, 165, 180, 195, 210, 225, 240, 255, 270, 285, 300, 315, 330,
            345, 360, 375, 390, 405, 420, 435, 450, 465, 480, 495
        ]
    )

    /// ALL standard - equivalent to practice mode with full range of heights
    static let allStandard = MeetStandard(
        id: "ALL",
        name: "ALL",
        description: "All possible heights (equivalent to practice mode)",
        isBuiltIn: true,
        heights: []  // Heights are generated dynamically in HeightSelectorView
    )

    /// Returns all available standards (built-in + custom)
    var allStandards: [MeetStandard] {
        var standards = [nfhsStandard, MeetStandardsManager.fhsaaStandard, MeetStandardsManager.allStandard]
        standards.append(contentsOf: customStandards)
        return standards
    }

    /// Loads the NFHS configuration from UserDefaults
    private func loadNFHSConfig() {
        if let data = UserDefaults.standard.data(forKey: "nfhsStandardConfig") {
            if let decoded = try? JSONDecoder().decode(MeetStandard.self, from: data) {
                nfhsStandard = decoded
                return
            }
        }

        // Default to the static NFHS standard if no saved configuration
        nfhsStandard = MeetStandardsManager.nfhsStandard
    }

    /// Saves the NFHS configuration to UserDefaults
    func saveNFHSConfig(_ config: MeetStandard) {
        if config.id == "NFHS" {
            nfhsStandard = config
            if let encoded = try? JSONEncoder().encode(config) {
                UserDefaults.standard.set(encoded, forKey: "nfhsStandardConfig")
            }
        }
    }

    /// Returns the default standard to use for new meets
    var defaultStandard: MeetStandard {
        if let standard = allStandards.first(where: { $0.id == defaultMeetStandard }) {
            return standard
        }
        return MeetStandardsManager.nfhsStandard
    }

    /// Sets the default standard for new meets
    func setDefaultStandard(_ standardId: String) {
        defaultMeetStandard = standardId
    }

    /// Gets a standard by ID
    func getStandard(id: String) -> MeetStandard? {
        return allStandards.first(where: { $0.id == id })
    }

    // MARK: - Custom Standards Management

    /// Adds a new custom standard
    func addCustomStandard(_ standard: MeetStandard) {
        customStandards.append(standard)
        saveCustomStandards()
    }

    /// Updates an existing custom standard
    func updateCustomStandard(_ standard: MeetStandard) {
        if let index = customStandards.firstIndex(where: { $0.id == standard.id }) {
            customStandards[index] = standard
            saveCustomStandards()
        }
    }

    /// Deletes a custom standard
    func deleteCustomStandard(id: String) {
        customStandards.removeAll(where: { $0.id == id })

        // If the deleted standard was the default, reset to NFHS
        if defaultMeetStandard == id {
            defaultMeetStandard = "NFHS"
        }

        saveCustomStandards()
    }

    /// Loads custom standards from UserDefaults
    private func loadCustomStandards() {
        if let data = UserDefaults.standard.data(forKey: "customMeetStandards") {
            if let decoded = try? JSONDecoder().decode([MeetStandard].self, from: data) {
                customStandards = decoded
                return
            }
        }

        // Default to empty array if no saved standards or decoding fails
        customStandards = []
    }

    /// Saves custom standards to UserDefaults
    private func saveCustomStandards() {
        if let encoded = try? JSONEncoder().encode(customStandards) {
            UserDefaults.standard.set(encoded, forKey: "customMeetStandards")
        }
    }

    /// Creates a new custom standard from a CSV string
    func importFromCSV(_ csvString: String) -> MeetStandard? {
        let lines = csvString.components(separatedBy: .newlines)

        // Need at least one line for the name and one for heights
        guard lines.count >= 2 else { return nil }

        // First line should be the name
        let name = lines[0].trimmingCharacters(in: .whitespacesAndNewlines)

        // Second line should be comma-separated heights
        let heightsString = lines[1].trimmingCharacters(in: .whitespacesAndNewlines)
        let heightComponents = heightsString.components(separatedBy: ",")

        var heights: [Double] = []
        for component in heightComponents {
            if let height = Double(component.trimmingCharacters(in: .whitespacesAndNewlines)) {
                heights.append(height)
            }
        }

        // Need at least one valid height
        guard !heights.isEmpty else { return nil }

        // Create a new custom standard
        return MeetStandard(
            id: UUID().uuidString,
            name: name,
            description: "Custom standard",
            isBuiltIn: false,
            heights: heights
        )
    }

    /// Resets all standards to their default values
    func resetToDefaults() {
        // Reset custom standards
        customStandards = []

        // Reset NFHS standard to default
        nfhsStandard = MeetStandardsManager.nfhsStandard

        // Reset default standard to NFHS
        defaultMeetStandard = "NFHS"

        // Force UI update
        objectWillChange.send()
    }
}

/// Represents a meet height standard
struct MeetStandard: Identifiable, Codable, Equatable {
    var id: String
    var name: String
    var description: String
    var isBuiltIn: Bool
    var heights: [Double]

    // NFHS specific properties
    var isDynamic: Bool = false
    var startingHeight: Double = 0
    var increment: Double = 0
    var maxHeight: Double = 0

    /// Returns the next height after the given height
    func nextHeight(after currentHeight: Double) -> Double? {
        if isDynamic && id == "NFHS" {
            // For dynamic NFHS standard, calculate the next height based on increment
            if currentHeight < startingHeight {
                return startingHeight
            }

            // Calculate how many increments above the starting height
            let incrementsAbove = ceil((currentHeight - startingHeight) / increment)

            // Calculate the next height
            let nextHeight = startingHeight + (incrementsAbove * increment)

            // Check if we've reached the maximum height
            if maxHeight > 0 && nextHeight > maxHeight {
                return nil
            }

            return nextHeight
        } else {
            // For fixed standards, find the next height in the sorted list
            let sortedHeights = heights.sorted()
            return sortedHeights.first(where: { $0 > currentHeight })
        }
    }

    /// Returns all heights for this standard
    func getAllHeights() -> [Double] {
        if isDynamic && id == "NFHS" {
            // Generate heights dynamically for NFHS
            var dynamicHeights: [Double] = []
            var currentHeight = startingHeight

            // Generate heights up to the maximum or a reasonable limit
            let limit = maxHeight > 0 ? maxHeight : 700 // 7 meters as a reasonable upper limit

            while currentHeight <= limit {
                dynamicHeights.append(currentHeight)
                currentHeight += increment
            }

            return dynamicHeights
        } else {
            // Return the fixed heights
            return heights.sorted()
        }
    }
}
