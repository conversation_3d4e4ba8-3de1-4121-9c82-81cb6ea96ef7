import Foundation
import CoreData
import SwiftUI

/// Utility class for managing athletes in the app
/// Handles detection and resolution of multiple athletes
class AthleteManagerUtility {
    // MARK: - Singleton
    
    /// Shared instance
    static let shared = AthleteManagerUtility()
    
    // MARK: - Properties
    
    /// Notification name for when athletes are merged
    static let athletesMergedNotification = Notification.Name("AthletesMergedNotification")
    
    // MARK: - Initialization
    
    private init() {
        print("AthleteManagerUtility initialized")
    }
    
    // MARK: - Public Methods
    
    /// Checks if multiple athletes exist in the database
    /// - Parameter context: The managed object context to use
    /// - Returns: True if multiple athletes exist, false otherwise
    func hasMultipleAthletes(in context: NSManagedObjectContext) -> Bool {
        let count = getAthleteCount(in: context)
        return count > 1
    }
    
    /// Gets the count of athletes in the database
    /// - Parameter context: The managed object context to use
    /// - Returns: The number of athletes
    func getAthleteCount(in context: NSManagedObjectContext) -> Int {
        let fetchRequest = Athlete.fetchRequest(in: context)
        
        do {
            return try context.count(for: fetchRequest)
        } catch {
            print("Error counting athletes: \(error)")
            return 0
        }
    }
    
    /// Fetches all athletes from the database
    /// - Parameter context: The managed object context to use
    /// - Returns: Array of athletes
    func getAllAthletes(in context: NSManagedObjectContext) -> [Athlete] {
        return Athlete.fetch(
            in: context,
            sortDescriptors: [NSSortDescriptor(keyPath: \Athlete.name, ascending: true)]
        )
    }
    
    /// Merges multiple athletes by keeping one and transferring all sessions to it
    /// - Parameters:
    ///   - keepAthlete: The athlete to keep
    ///   - context: The managed object context to use
    /// - Returns: True if merge was successful, false otherwise
    func mergeAthletes(keepAthlete: Athlete, in context: NSManagedObjectContext) -> Bool {
        // Get all athletes
        let allAthletes = getAllAthletes(in: context)
        
        // Make sure we have the athlete to keep
        guard allAthletes.contains(where: { $0.objectID == keepAthlete.objectID }) else {
            print("Error: Athlete to keep not found in database")
            return false
        }
        
        // Transfer all sessions and poles from other athletes to the one we're keeping
        for athlete in allAthletes {
            // Skip the athlete we're keeping
            if athlete.objectID == keepAthlete.objectID {
                continue
            }
            
            print("Merging athlete \(athlete.name ?? "Unknown") into \(keepAthlete.name ?? "Unknown")")
            
            // Transfer sessions
            if let sessions = athlete.sessions?.allObjects as? [Session] {
                for session in sessions {
                    session.athlete = keepAthlete
                    print("Transferred session: \(session.title ?? "Untitled")")
                }
            }
            
            // Transfer poles
            if let poles = athlete.poles?.allObjects as? [Pole] {
                for pole in poles {
                    pole.athlete = keepAthlete
                    print("Transferred pole: \(pole.name ?? "Unnamed")")
                }
            }
            
            // Delete the other athlete
            context.delete(athlete)
            print("Deleted athlete: \(athlete.name ?? "Unknown")")
        }
        
        // Save changes
        do {
            try context.save()
            
            // Post notification that athletes were merged
            NotificationCenter.default.post(name: Self.athletesMergedNotification, object: nil)
            
            return true
        } catch {
            print("Error saving context after merging athletes: \(error)")
            return false
        }
    }
}
