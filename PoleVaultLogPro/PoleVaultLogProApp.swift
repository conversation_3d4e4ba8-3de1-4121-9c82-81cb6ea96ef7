//
//  PoleVaultLogProApp.swift
//  PoleVaultLogPro
//
//  Created by <PERSON> on 4/29/25.
//

import SwiftUI
import CoreData
import CloudKit

// MARK: - Sheet Presentation Models
struct DataRecoverySheetItem: Identifiable {
    let id = UUID()
}

@main
struct PoleVaultLogProApp: App {
    // Use the shared persistence controller for Core Data to avoid multiple instances
    let persistenceController = PersistenceController.shared

    // Initialize iCloud key-value store
    private let iCloudStore = NSUbiquitousKeyValueStore.default

    // State to track CloudKit availability
    @State private var cloudKitAvailable = true
    @State private var showCloudKitError = false
    @State private var cloudKitErrorMessage = ""

    // State to track if app needs restart after Core Data reset
    @State private var needsAppRestart = false
    @State private var showRestartAlert = false

    // State to track data recovery
    @State private var dataRecoverySheet: DataRecoverySheetItem?

    init() {
        // Print debug information
        AppConfiguration.printDebugInfo()

        // Check if app needs restart after Core Data reset
        if UserDefaults.standard.bool(forKey: "needsAppRestart") {
            needsAppRestart = true
            showRestartAlert = true
            print("App needs restart after Core Data reset")
        }

        // Check if Core Data has load errors
        if UserDefaults.standard.bool(forKey: "coreDataHasLoadError") ||
           UserDefaults.standard.bool(forKey: "needsCoreDataReset") {
            dataRecoverySheet = DataRecoverySheetItem()
            print("Core Data has load errors - showing recovery alert")
        }

        // Check if we need to reset Core Data due to model version issues
        checkForCoreDataReset()

        // Check CloudKit availability
        checkCloudKitAvailability()

        // Set up iCloud key-value store
        setupICloudKeyValueStore()

        // Set up CloudKit sync notification observer
        setupCloudKitSyncObserver()

        // Set up Core Data reset notification observer
        setupCoreDataResetObserver()

        // Set up Core Data load error observer
        setupCoreDataLoadErrorObserver()
    }

    /// Sets up observers for Core Data reset notifications
    private func setupCoreDataResetObserver() {
        // Observer for preparing for Core Data reset
        NotificationCenter.default.addObserver(
            forName: NSNotification.Name("PrepareForCoreDataReset"),
            object: nil,
            queue: .main
        ) { _ in
            print("Preparing for Core Data reset - releasing references")
            // Release any Core Data references here
        }

        // Observer for Core Data reset completion
        NotificationCenter.default.addObserver(
            forName: NSNotification.Name("CoreDataResetCompleted"),
            object: nil,
            queue: .main
        ) { _ in
            print("Core Data reset completed - refreshing views")
            // Refresh views that depend on Core Data
        }
    }

    /// Checks if we need to reset Core Data due to model version issues
    private func checkForCoreDataReset() {
        // Check if this is the first launch after an update
        let currentVersion = Bundle.main.object(forInfoDictionaryKey: "CFBundleShortVersionString") as? String ?? "1.0"
        let lastVersion = UserDefaults.standard.string(forKey: "lastAppVersion") ?? ""

        if currentVersion != lastVersion {
            print("App version changed from \(lastVersion) to \(currentVersion)")

            // Check if we need to force a reset due to Core Data model changes
            let forceReset = UserDefaults.standard.bool(forKey: "forceDataResetOnNextLaunch")

            if forceReset {
                print("Forcing Core Data reset due to model changes")
                // Reset the Core Data store
                _ = CoreDataResetUtility.resetCoreDataStore()

                // Reset the flag
                UserDefaults.standard.set(false, forKey: "forceDataResetOnNextLaunch")
            }

            // Save the current version
            UserDefaults.standard.set(currentVersion, forKey: "lastAppVersion")
        }
    }

    /// Sets up iCloud key-value store and its observer
    private func setupICloudKeyValueStore() {
        // Synchronize with iCloud
        iCloudStore.synchronize()

        // Add observer for external changes
        NotificationCenter.default.addObserver(
            forName: NSUbiquitousKeyValueStore.didChangeExternallyNotification,
            object: iCloudStore,
            queue: .main
        ) { notification in
            handleICloudStoreChange(notification)
        }
    }

    /// Handles changes to the iCloud key-value store
    private func handleICloudStoreChange(_ notification: Notification) {
        guard let userInfo = notification.userInfo,
              let reasonForChange = userInfo[NSUbiquitousKeyValueStoreChangeReasonKey] as? Int,
              let changedKeys = userInfo[NSUbiquitousKeyValueStoreChangedKeysKey] as? [String] else {
            return
        }

        // Only process server changes (1 = NSUbiquitousKeyValueStoreServerChange, 2 = NSUbiquitousKeyValueStoreInitialSyncChange)
        guard reasonForChange == 1 || reasonForChange == 2 else {
            return
        }

        // Update UserDefaults with the new values from iCloud
        for key in changedKeys {
            if let value = iCloudStore.object(forKey: key) {
                UserDefaults.standard.set(value, forKey: key)

                // Post notification for specific preference changes
                if key == "useMetricSystem" || key == "showSecondaryUnits" {
                    NotificationCenter.default.post(name: NSNotification.Name("UnitPreferenceChanged"), object: nil)
                } else if key == "selectedAppIcon" {
                    NotificationCenter.default.post(name: NSNotification.Name("AppThemeChanged"), object: nil)
                }
            }
        }
    }

    /// Sets up observer for CloudKit sync notifications
    private func setupCloudKitSyncObserver() {
        // Observe NSPersistentCloudKitContainer's built-in event notifications
        NotificationCenter.default.addObserver(
            forName: NSPersistentCloudKitContainer.eventChangedNotification,
            object: nil,
            queue: .main
        ) { notification in
            guard let event = notification.userInfo?[NSPersistentCloudKitContainer.eventNotificationUserInfoKey]
                as? NSPersistentCloudKitContainer.Event else {
                return
            }

            switch event.type {
            case .import:
                print("CloudKit import event: \(event)")
            case .export:
                print("CloudKit export event: \(event)")
            case .setup:
                print("CloudKit setup event: \(event)")
            @unknown default:
                print("Unknown CloudKit event: \(event)")
            }
        }
    }

    /// Sets up observer for Core Data load errors
    private func setupCoreDataLoadErrorObserver() {
        NotificationCenter.default.addObserver(
            forName: NSNotification.Name("CoreDataLoadError"),
            object: nil,
            queue: .main
        ) { notification in
            print("Core Data load error detected")

            // Show the data recovery alert
            self.dataRecoverySheet = DataRecoverySheetItem()
        }
    }

    /// Checks if CloudKit is available and shows an alert if it's not
    private func checkCloudKitAvailability() {
        CKContainer.default().accountStatus { status, error in
            DispatchQueue.main.async {
                switch status {
                case .available:
                    // CloudKit is available, nothing to do
                    self.cloudKitAvailable = true

                case .noAccount:
                    // No iCloud account
                    self.cloudKitAvailable = false
                    self.cloudKitErrorMessage = "No iCloud account found. Your data will be stored locally only."
                    self.showCloudKitError = true

                case .restricted:
                    // iCloud is restricted
                    self.cloudKitAvailable = false
                    self.cloudKitErrorMessage = "iCloud access is restricted. Your data will be stored locally only."
                    self.showCloudKitError = true

                case .couldNotDetermine:
                    // Could not determine status
                    self.cloudKitAvailable = false
                    self.cloudKitErrorMessage = "Could not determine iCloud account status. Your data will be stored locally only."
                    self.showCloudKitError = true

                case .temporarilyUnavailable:
                    // iCloud is temporarily unavailable
                    self.cloudKitAvailable = false
                    self.cloudKitErrorMessage = "iCloud is temporarily unavailable. Your data will be stored locally only."
                    self.showCloudKitError = true

                @unknown default:
                    // Unknown status
                    self.cloudKitAvailable = false
                    self.cloudKitErrorMessage = "Unknown iCloud account status. Your data will be stored locally only."
                    self.showCloudKitError = true
                }
            }
        }
    }

    var body: some Scene {
        WindowGroup {
            ContentView()
                .environment(\.managedObjectContext, persistenceController.container.viewContext)
                .alert(isPresented: $showCloudKitError) {
                    Alert(
                        title: Text("iCloud Sync Unavailable"),
                        message: Text(cloudKitErrorMessage),
                        dismissButton: .default(Text("Continue Offline"))
                    )
                }
                .alert(isPresented: $showRestartAlert) {
                    Alert(
                        title: Text("⚠️ RESTART REQUIRED ⚠️"),
                        message: Text("The app's data has been reset.\n\nIMPORTANT: You MUST completely close the app by swiping it up from the app switcher, then restart it for the changes to take effect.\n\nIf you don't restart, the app will crash when you try to use it."),
                        dismissButton: .default(Text("I'll Restart Now")) {
                            // Clear the restart flag when the alert is dismissed
                            UserDefaults.standard.set(false, forKey: "needsAppRestart")
                        }
                    )
                }
                .onAppear {
                    // If the app was restarted after a reset, clear the flag
                    if needsAppRestart {
                        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                            // This delay ensures the app has fully loaded before showing the alert
                            showRestartAlert = true
                        }
                    }
                }
                .sheet(item: $dataRecoverySheet) { _ in
                    DataRecoveryAlertView()
                }
        }
    }
}
