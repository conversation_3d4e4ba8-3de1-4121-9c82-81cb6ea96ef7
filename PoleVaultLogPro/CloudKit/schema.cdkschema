DEFINE SCHEMA

RECORD TYPE CD_Athlete (
    "___createTime" TIMESTAMP QUERYABLE SORTABLE,
    "___createdBy" REFERENCE QUERYABLE,
    "___etag" STRING QUERYABLE,
    "___modTime" TIMESTAMP QUERYABLE SORTABLE,
    "___modifiedBy" REFERENCE QUERYABLE,
    "___recordID" REFERENCE QUERYABLE,
    "id" STRING QUERYABLE SEARCHABLE,
    "name" STRING QUERYABLE SEARCHABLE SORTABLE,
    "dateOfBirth" TIMESTAMP QUERYABLE SORTABLE,
    "dominantHand" STRING QUERYABLE SEARCHABLE,
    "personalBestCm" DOUBLE QUERYABLE SORTABLE,
    "personalBestIn" DOUBLE QUERYABLE SORTABLE,
    GRANT READ, WRITE, CREATE, DELETE TO "_creator"
);

RECORD TYPE CD_Session (
    "___createTime" TIMESTAMP QUERYABLE SORTABLE,
    "___createdBy" REFERENCE QUERYABLE,
    "___etag" STRING QUERYABLE,
    "___modTime" TIMESTAMP QUERYABLE SORTABLE,
    "___modifiedBy" REFERENCE QUERYABLE,
    "___recordID" REFERENCE QUERYABLE,
    "id" STRING QUERYABLE SEARCHABLE,
    "title" STRING QUERYABLE SEARCHABLE SORTABLE,
    "date" TIMESTAMP QUERYABLE SORTABLE,
    "type" STRING QUERYABLE SEARCHABLE,
    "location" STRING QUERYABLE SEARCHABLE,
    "weather" STRING QUERYABLE SEARCHABLE,
    "notesAthlete" STRING QUERYABLE SEARCHABLE,
    "notesCoach" STRING QUERYABLE SEARCHABLE,
    "bestHeightCm" DOUBLE QUERYABLE SORTABLE,
    "bestHeightIn" DOUBLE QUERYABLE SORTABLE,
    "heightStandard" STRING QUERYABLE SEARCHABLE,
    "athlete" REFERENCE QUERYABLE,
    GRANT READ, WRITE, CREATE, DELETE TO "_creator"
);

RECORD TYPE CD_Jump (
    "___createTime" TIMESTAMP QUERYABLE SORTABLE,
    "___createdBy" REFERENCE QUERYABLE,
    "___etag" STRING QUERYABLE,
    "___modTime" TIMESTAMP QUERYABLE SORTABLE,
    "___modifiedBy" REFERENCE QUERYABLE,
    "___recordID" REFERENCE QUERYABLE,
    "id" STRING QUERYABLE SEARCHABLE,
    "barHeightCm" DOUBLE QUERYABLE SORTABLE,
    "barHeightIn" DOUBLE QUERYABLE SORTABLE,
    "result" STRING QUERYABLE SEARCHABLE,
    "resultCode" STRING QUERYABLE SEARCHABLE,
    "comment" STRING QUERYABLE SEARCHABLE,
    "order" INT64 QUERYABLE SORTABLE,
    "attemptIndex" INT64 QUERYABLE SORTABLE,
    "columnIndex" INT64 QUERYABLE SORTABLE,
    "useBar" INT64 QUERYABLE,
    "handHoldCm" DOUBLE QUERYABLE SORTABLE,
    "runStartCm" DOUBLE QUERYABLE SORTABLE,
    "standardCm" DOUBLE QUERYABLE SORTABLE,
    "takeOffStepCm" DOUBLE QUERYABLE SORTABLE,
    "session" REFERENCE QUERYABLE,
    "pole" REFERENCE QUERYABLE,
    GRANT READ, WRITE, CREATE, DELETE TO "_creator"
);

RECORD TYPE CD_JumpMedia (
    "___createTime" TIMESTAMP QUERYABLE SORTABLE,
    "___createdBy" REFERENCE QUERYABLE,
    "___etag" STRING QUERYABLE,
    "___modTime" TIMESTAMP QUERYABLE SORTABLE,
    "___modifiedBy" REFERENCE QUERYABLE,
    "___recordID" REFERENCE QUERYABLE,
    "id" STRING QUERYABLE SEARCHABLE,
    "type" STRING QUERYABLE SEARCHABLE,
    "assetIdentifier" STRING QUERYABLE SEARCHABLE,
    "posterTime" DOUBLE QUERYABLE SORTABLE,
    "jump" REFERENCE QUERYABLE,
    GRANT READ, WRITE, CREATE, DELETE TO "_creator"
);

RECORD TYPE CD_Pole (
    "___createTime" TIMESTAMP QUERYABLE SORTABLE,
    "___createdBy" REFERENCE QUERYABLE,
    "___etag" STRING QUERYABLE,
    "___modTime" TIMESTAMP QUERYABLE SORTABLE,
    "___modifiedBy" REFERENCE QUERYABLE,
    "___recordID" REFERENCE QUERYABLE,
    "id" STRING QUERYABLE SEARCHABLE,
    "name" STRING QUERYABLE SEARCHABLE SORTABLE,
    "brand" STRING QUERYABLE SEARCHABLE,
    "color" STRING QUERYABLE SEARCHABLE,
    "lengthCm" DOUBLE QUERYABLE SORTABLE,
    "lengthIn" DOUBLE QUERYABLE SORTABLE,
    "weight" DOUBLE QUERYABLE SORTABLE,
    "order" INT64 QUERYABLE SORTABLE,
    "athlete" REFERENCE QUERYABLE,
    GRANT READ, WRITE, CREATE, DELETE TO "_creator"
);

RECORD TYPE CD_GridHeight (
    "___createTime" TIMESTAMP QUERYABLE SORTABLE,
    "___createdBy" REFERENCE QUERYABLE,
    "___etag" STRING QUERYABLE,
    "___modTime" TIMESTAMP QUERYABLE SORTABLE,
    "___modifiedBy" REFERENCE QUERYABLE,
    "___recordID" REFERENCE QUERYABLE,
    "id" STRING QUERYABLE SEARCHABLE,
    "heightCm" DOUBLE QUERYABLE SORTABLE,
    "heightIn" DOUBLE QUERYABLE SORTABLE,
    "order" INT64 QUERYABLE SORTABLE,
    "session" REFERENCE QUERYABLE,
    GRANT READ, WRITE, CREATE, DELETE TO "_creator"
);

RECORD TYPE CD_SyncMarker (
    "___createTime" TIMESTAMP QUERYABLE SORTABLE,
    "___createdBy" REFERENCE QUERYABLE,
    "___etag" STRING QUERYABLE,
    "___modTime" TIMESTAMP QUERYABLE SORTABLE,
    "___modifiedBy" REFERENCE QUERYABLE,
    "___recordID" REFERENCE QUERYABLE,
    "id" STRING QUERYABLE SEARCHABLE,
    "timestamp" DOUBLE QUERYABLE SORTABLE,
    "deviceIdentifier" STRING QUERYABLE SEARCHABLE,
    GRANT READ, WRITE, CREATE, DELETE TO "_creator"
);
