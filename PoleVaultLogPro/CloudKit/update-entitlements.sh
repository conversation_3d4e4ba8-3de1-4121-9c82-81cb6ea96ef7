#!/bin/bash

# This script updates the CloudKit environment in the entitlements file
# It's meant to be run as a build phase in Xcode

# Path to the entitlements file
ENTITLEMENTS_FILE="${PROJECT_DIR}/PoleVaultLogPro/PoleVaultLogPro.entitlements"

# Determine environment based on build configuration or user-defined setting
if [ -n "${CLOUDKIT_ENVIRONMENT}" ]; then
    # Use the user-defined setting if available
    ENVIRONMENT="${CLOUDKIT_ENVIRONMENT}"
else
    # Fall back to configuration-based determination
    if [ "${CONFIGURATION}" = "Debug" ]; then
        ENVIRONMENT="Development"
    else
        ENVIRONMENT="Production"
    fi
fi

echo "Updating entitlements file for CloudKit environment: $ENVIRONMENT"

# Check if entitlements file exists
if [ ! -f "$ENTITLEMENTS_FILE" ]; then
    echo "Error: Entitlements file not found at $ENTITLEMENTS_FILE"
    exit 1
fi

# Update the environment in the entitlements file
/usr/libexec/PlistBuddy -c "Set :com.apple.developer.icloud-container-environment $ENVIRONMENT" "$ENTITLEMENTS_FILE"

if [ $? -eq 0 ]; then
    echo "Entitlements file updated successfully!"
else
    echo "Failed to update entitlements file. Check the error message above."
    exit 1
fi

echo "Done!"
