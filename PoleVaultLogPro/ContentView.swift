//
//  ContentView.swift
//  PoleVaultLogPro
//
//  Created by <PERSON> on 4/29/25.
//

import SwiftUI
import CoreData

// MARK: - Sheet Presentation Models
struct AthleteProfileSheetItem: Identifiable {
    let id = UUID()
}

struct MultipleAthletesSheetItem: Identifiable {
    let id = UUID()
}

struct ContentView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @State private var needsRefresh = false
    @State private var showingAthleteCreationPrompt = false
    @State private var athleteProfileSheet: AthleteProfileSheetItem?
    @State private var multipleAthletesSheet: MultipleAthletesSheetItem?

    // Fetch athletes to check if any exist
    @FetchRequest(
        sortDescriptors: [NSSortDescriptor(keyPath: \Athlete.name, ascending: true)],
        animation: .default)
    private var athletes: FetchedResults<Athlete>

    // Setup notification observers
    private let resetCompletedPublisher = NotificationCenter.default.publisher(for: NSNotification.Name("CoreDataResetCompleted"))
    private let athletesMergedPublisher = NotificationCenter.default.publisher(for: AthleteManagerUtility.athletesMergedNotification)

    var body: some View {
        ZStack {
            MainTabView()
                .environment(\.managedObjectContext, PersistenceController.shared.container.viewContext)
                .onAppear {
                    // Clear any reset flags
                    if UserDefaults.standard.bool(forKey: "appWasReset") {
                        UserDefaults.standard.set(false, forKey: "appWasReset")
                    }

                    // Log that Core Data is set up
                    print("Core Data is initialized and ready")

                    // Check for multiple athletes or no athletes
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                        checkAthleteStatus()
                    }
                }
                .onReceive(resetCompletedPublisher) { _ in
                    print("Received notification that Core Data was reset")

                    // Force a UI refresh by toggling the ID
                    needsRefresh.toggle()
                }
                .onReceive(athletesMergedPublisher) { _ in
                    print("Received notification that athletes were merged")

                    // Force a UI refresh
                    needsRefresh.toggle()
                }
                .id(needsRefresh) // Force view refresh when Core Data is reset
                .sheet(item: $athleteProfileSheet) { _ in
                    AthleteProfileView()
                        .environment(\.managedObjectContext, viewContext)
                }
                .sheet(item: $multipleAthletesSheet) { _ in
                    MultipleAthletesView()
                        .environment(\.managedObjectContext, viewContext)
                }
                .alert("Welcome to PoleVaultLogPro!", isPresented: $showingAthleteCreationPrompt) {
                    Button("Create Athlete Profile", role: .none) {
                        athleteProfileSheet = AthleteProfileSheetItem()
                    }
                    Button("Later", role: .cancel) {
                        // User will create athlete later
                    }
                } message: {
                    Text("To get started, please create your athlete profile. This will help personalize your pole vault tracking experience.")
                }
        }
    }

    /// Checks if we need to show the athlete creation prompt or multiple athletes view
    private func checkAthleteStatus() {
        if AthleteManagerUtility.shared.hasMultipleAthletes(in: viewContext) {
            // Multiple athletes detected, show the selection view
            multipleAthletesSheet = MultipleAthletesSheetItem()
        } else if athletes.isEmpty {
            // No athletes, show the creation prompt
            showingAthleteCreationPrompt = true
        }
    }
}

#Preview {
    ContentView()
        .environment(\.managedObjectContext, PersistenceController(inMemory: true).container.viewContext)
}
