import Foundation
import CoreData
import SwiftUI

class SessionViewModel: ObservableObject, Identifiable {
    @Published var session: Session
    @Published var consecutiveMisses: Int = 0
    @Published var isOut: Bool = false

    // Add an ID to help with state preservation
    var id: String {
        return session.id ?? UUID().uuidString
    }

    init(session: Session) {
        self.session = session
        updateConsecutiveMissCount()
    }

    /// Updates the consecutive miss count based on the session's jumps
    func updateConsecutiveMissCount() {
        // We'll use our more comprehensive recalculateOutStatus method
        // but preserve the behavior where once an athlete is out, they stay out
        let wasOut = isOut

        // Call recalculateOutStatus to get the current state
        recalculateOutStatus()

        // If the athlete was previously out, keep them out
        // This maintains the original behavior where once out, always out
        // unless explicitly reset
        if wasOut {
            isOut = true
        }
    }

    /// Explicitly reset the out status (used when deleting heights or changing a miss to a pass/make)
    func resetOutStatus() {
        #if DEBUG
        print("Explicitly resetting out status")
        #endif

        // Reset the out status
        consecutiveMisses = 0
        isOut = false

        // Force UI update
        objectWillChange.send()

        #if DEBUG
        print("After resetOutStatus: consecutiveMisses = \(consecutiveMisses), isOut = \(isOut)")
        #endif
    }

    // We no longer need the decrementConsecutiveMisses method
    // since we're now fully recalculating the consecutive misses count
    // in the recalculateOutStatus method

    /// Recalculate the out status based on the current jumps
    /// This is used when a jump is changed from a miss to a make or pass,
    /// or when heights are removed, or when any jump is edited
    func recalculateOutStatus() {
        // Check if this is a meet - only enforce 3 misses rule for meets
        let isMeet = (session.type?.lowercased() == "meet")

        // For practices, we can simplify this process significantly
        if !isMeet {
            // For practices, always set isOut to false regardless of consecutive misses
            isOut = false

            // Still count consecutive misses for display purposes
            updateConsecutiveMissesForPractice()

            // Force UI update for all cells
            objectWillChange.send()

            // Notify any observers that the session has changed
            NotificationCenter.default.post(name: NSNotification.Name("SessionDataChanged"), object: session)

            return
        }

        // For meets, continue with the full calculation
        #if DEBUG
        print("Starting recalculateOutStatus: consecutiveMisses = \(consecutiveMisses), isOut = \(isOut)")
        #endif

        // Save the current out status
        let wasOut = isOut

        // Reset the out status
        isOut = false
        consecutiveMisses = 0

        #if DEBUG
        print("After reset: consecutiveMisses = \(consecutiveMisses), isOut = \(isOut)")
        #endif

        // Get all jumps for this session
        guard let jumps = session.jumps?.allObjects as? [Jump] else {
            return
        }

        // Get all heights in the grid
        let heights = session.getGridHeights()

        // Group jumps by height
        var jumpsByHeight: [Double: [Jump]] = [:]
        for height in heights {
            jumpsByHeight[height] = jumps.filter { abs($0.barHeightCm - height) < 0.1 }
        }

        // Sort heights in ascending order (lowest to highest)
        let sortedHeights = heights.sorted()

        #if DEBUG
        print("Processing \(sortedHeights.count) heights: \(sortedHeights)")
        #endif

        // Track consecutive misses across all heights
        var consecutiveMissesCount = 0
        var isAthleteOut = false

        // Process each height in order
        for height in sortedHeights {
            #if DEBUG
            print("Processing height: \(height)")
            #endif

            // Get jumps at this height
            let jumpsAtHeight = jumpsByHeight[height] ?? []

            // Sort jumps by attempt index (1st, 2nd, 3rd)
            let sortedJumps = jumpsAtHeight.sorted { $0.attemptIndex < $1.attemptIndex }

            #if DEBUG
            print("Jumps at height \(height): \(sortedJumps.count)")
            for jump in sortedJumps {
                print("  Jump: attempt=\(jump.attemptIndex), result=\(jump.result ?? "unknown")")
            }
            #endif

            // Check if there's a make at this height
            let hasMake = sortedJumps.contains { $0.result == "make" }

            // If there's a make at this height, reset the consecutive misses count
            // This ensures that a make clears the OUT status for future heights
            if hasMake {
                #if DEBUG
                print("Found make at height \(height), resetting consecutiveMissesCount")
                #endif
                consecutiveMissesCount = 0
                continue // Skip to the next height
            }

            // Pass doesn't reset the consecutive misses count
            // It's neither a miss nor a make - we don't increment or reset the counter

            // Process each jump at this height
            var missesAtThisHeight = 0
            var passesAtThisHeight = 0

            for jump in sortedJumps {
                if jump.result == "miss" {
                    missesAtThisHeight += 1
                    consecutiveMissesCount += 1
                    #if DEBUG
                    print("Found miss at height \(height), consecutiveMissesCount = \(consecutiveMissesCount)")
                    #endif
                    if consecutiveMissesCount >= 3 {
                        isAthleteOut = true
                        #if DEBUG
                        print("Athlete is now OUT with \(consecutiveMissesCount) consecutive misses")
                        #endif
                    }
                } else if jump.result == "pass" {
                    passesAtThisHeight += 1
                    #if DEBUG
                    print("Found pass at height \(height), passesAtThisHeight = \(passesAtThisHeight)")
                    #endif
                }
            }

            #if DEBUG
            print("After processing height \(height): consecutiveMissesCount = \(consecutiveMissesCount), isAthleteOut = \(isAthleteOut)")
            #endif
        }

        // Update the out status and consecutive misses count
        isOut = isAthleteOut
        consecutiveMisses = consecutiveMissesCount

        #if DEBUG
        print("After recalculation: consecutiveMisses = \(consecutiveMisses), isOut = \(isOut)")
        #endif

        // If the athlete is no longer out, provide feedback
        if wasOut && !isOut {
            // Provide haptic feedback for being back in
            let generator = UINotificationFeedbackGenerator()
            generator.notificationOccurred(.success)

            #if DEBUG
            print("Providing haptic feedback for being back in")
            #endif
        }

        // Force UI update for all cells
        objectWillChange.send()

        // Notify any observers that the session has changed
        NotificationCenter.default.post(name: NSNotification.Name("SessionDataChanged"), object: session)

        #if DEBUG
        print("Final state after recalculateOutStatus: consecutiveMisses = \(consecutiveMisses), isOut = \(isOut)")
        #endif
    }

    /// A simplified version of consecutive miss counting for practice sessions
    private func updateConsecutiveMissesForPractice() {
        // Get all jumps for this session
        guard let jumps = session.jumps?.allObjects as? [Jump] else {
            consecutiveMisses = 0
            return
        }

        // Sort jumps by order (most recent first)
        let sortedJumps = jumps.sorted { $0.order > $1.order }

        // Count consecutive misses from the most recent jump
        var count = 0
        for jump in sortedJumps {
            if jump.result == "miss" {
                count += 1
            } else if jump.result == "make" {
                // A make breaks the consecutive miss streak
                break
            }
            // Pass doesn't break the streak but doesn't count as a miss either
        }

        consecutiveMisses = count
    }

    /// Updates the consecutive miss count when a new result is recorded
    func updateConsecutiveMisses(for result: String) {
        // Check if this is a meet - only enforce 3 misses rule for meets
        let isMeet = (session.type?.lowercased() == "meet")

        if result == "make" {
            // A make resets the consecutive miss counter
            // but does NOT reset the out status by itself
            // (recalculateOutStatus will be called separately)
            consecutiveMisses = 0

            // Soft haptic feedback for make (O)
            let generator = UIImpactFeedbackGenerator(style: .light)
            generator.impactOccurred()
        } else if result == "miss" {
            consecutiveMisses += 1

            // Check if athlete is out - only for meets
            if isMeet && consecutiveMisses >= 3 && !isOut {
                isOut = true

                // Trigger triple-warn haptic feedback for OUT
                let generator = UIImpactFeedbackGenerator(style: .heavy)
                generator.prepare()

                // Triple pulse with delays
                generator.impactOccurred()

                // Schedule the second and third pulses with delays
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    generator.impactOccurred()

                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                        generator.impactOccurred()
                    }
                }
            } else if !isMeet {
                // For practices, always ensure isOut is false
                isOut = false
            }
        } else if result == "pass" {
            // Pass doesn't affect the consecutive misses counter directly
            // It's neither a miss nor a make - we don't increment or reset the counter

            // Soft haptic feedback for pass (P)
            let generator = UIImpactFeedbackGenerator(style: .light)
            generator.impactOccurred()
        }

        // Force UI update
        objectWillChange.send()
    }

    /// Checks if pass is allowed based on the new requirements
    func isPassAllowed(at height: Double) -> Bool {
        // For practices, always allow pass
        if session.type?.lowercased() == "practice" {
            return true
        }

        guard let jumps = session.jumps?.allObjects as? [Jump] else {
            return true // No jumps yet, so pass is allowed
        }

        // Check if there's a make at this height
        let hasMakeAtThisHeight = jumps.contains { jump in
            abs(jump.barHeightCm - height) < 0.1 && jump.result == "make"
        }

        // Pass is only allowed if there's no make at this height
        return !hasMakeAtThisHeight
    }

    /// Checks if a specific cell should be disabled due to pass or make logic
    func isCellDisabledDueToPass(height: Double, attemptIndex: Int16) -> Bool {
        // For practices, don't disable cells due to pass
        if session.type?.lowercased() == "practice" {
            return false
        }

        guard let jumps = session.jumps?.allObjects as? [Jump] else {
            return false
        }

        // Check if this cell already has a jump - if so, don't disable it
        let existingJump = jumps.first { jump in
            abs(jump.barHeightCm - height) < 0.1 && jump.attemptIndex == attemptIndex
        }

        if existingJump != nil {
            return false
        }

        // Get all previous attempts at this height
        let previousAttempts = jumps.filter { jump in
            abs(jump.barHeightCm - height) < 0.1 && jump.attemptIndex < attemptIndex
        }

        // Check if any previous attempt is a pass
        let hasPreviousPass = previousAttempts.contains { jump in
            jump.result == "pass"
        }

        // If there's a pass on any previous attempt at this height, disable this cell
        return hasPreviousPass
    }

    /// Checks if a specific cell should be disabled due to a make at the same height
    func isCellDisabledDueToMake(height: Double, attemptIndex: Int16) -> Bool {
        // For practices, don't disable cells due to make
        if session.type?.lowercased() == "practice" {
            return false
        }

        guard let jumps = session.jumps?.allObjects as? [Jump] else {
            return false
        }

        // Check if this cell already has a jump - if so, don't disable it
        let existingJump = jumps.first { jump in
            abs(jump.barHeightCm - height) < 0.1 && jump.attemptIndex == attemptIndex
        }

        if existingJump != nil {
            return false
        }

        // Check if there's a make at this height
        let hasMakeAtThisHeight = jumps.contains { jump in
            abs(jump.barHeightCm - height) < 0.1 && jump.result == "make"
        }

        // If there's a make at this height, disable all remaining attempts
        return hasMakeAtThisHeight
    }

    /// Checks if a specific cell should show "OUT"
    func shouldShowOut(height: Double, attemptIndex: Int16) -> Bool {
        // For practices, never show OUT
        if session.type?.lowercased() == "practice" {
            return false
        }

        // If the athlete is not out or doesn't have 3+ consecutive misses, don't show OUT
        if !isOut || consecutiveMisses < 3 {
            return false
        }

        // Check if this cell already has a jump
        guard let jumps = session.jumps?.allObjects as? [Jump] else {
            return false
        }

        let existingJump = jumps.first { jump in
            abs(jump.barHeightCm - height) < 0.1 && jump.attemptIndex == attemptIndex
        }

        // If there's already a jump in this cell, don't show OUT
        return existingJump == nil
    }

    /// Checks if a cell should be disabled due to the athlete being out
    func isCellDisabledDueToOut(height: Double, attemptIndex: Int16) -> Bool {
        // For practices, never disable cells due to OUT
        if session.type?.lowercased() == "practice" {
            return false
        }

        // If the athlete is not out or doesn't have 3+ consecutive misses, the cell is not disabled due to being out
        if !isOut || consecutiveMisses < 3 {
            return false
        }

        // Check if this cell already has a jump
        guard let jumps = session.jumps?.allObjects as? [Jump] else {
            return false
        }

        let existingJump = jumps.first { jump in
            abs(jump.barHeightCm - height) < 0.1 && jump.attemptIndex == attemptIndex
        }

        // If there's already a jump in this cell, don't disable it
        if existingJump != nil {
            return false
        }

        // If the athlete is out, disable all empty cells
        return true
    }
}
