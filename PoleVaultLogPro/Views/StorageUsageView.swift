import SwiftUI

/// A view that displays media link information
struct StorageUsageView: View {
    // State for storage usage
    @State private var photoCount: Int = 0
    @State private var videoCount: Int = 0
    @State private var isRefreshing = false

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text("Media Links")
                    .font(.headline)

                Spacer()

                Button(action: {
                    refreshStorageUsage()
                }) {
                    Image(systemName: "arrow.clockwise")
                        .font(.caption)
                        .foregroundColor(AppTheme.accentColor)
                }
                .disabled(isRefreshing)
            }

            // Media links by type
            HStack {
                Text("Linked Media")
                    .font(.subheadline)

                Spacer()

                VStack(alignment: .trailing) {
                    HStack(spacing: 4) {
                        Text("Photos:")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Text("\(photoCount)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .fontWeight(.bold)
                    }

                    HStack(spacing: 4) {
                        Text("Videos:")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Text("\(videoCount)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .fontWeight(.bold)
                    }
                }
            }



            // Total links
            HStack {
                Text("Total Links")
                    .font(.subheadline)
                    .fontWeight(.bold)

                Spacer()

                VStack(alignment: .trailing) {
                    Text("\(photoCount + videoCount) media items")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .fontWeight(.bold)
                }
            }

            if isRefreshing {
                HStack {
                    Spacer()
                    ProgressView()
                        .scaleEffect(0.7)
                    Spacer()
                }
            }
        }
        .padding(.vertical, 8)
        .onAppear {
            refreshStorageUsage()
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("MediaFilesChanged"))) { _ in
            // Refresh the view when media files are changed
            refreshStorageUsage()
        }
    }

    /// Refreshes the storage usage information
    private func refreshStorageUsage() {
        isRefreshing = true

        // Use a background task to avoid blocking the UI
        Task {
            // Count media items by type
            let mediaTypeCounts = MediaStorageManager.shared.countMediaItemsByType()

            // Update the UI on the main thread
            DispatchQueue.main.async {
                self.photoCount = mediaTypeCounts.photos
                self.videoCount = mediaTypeCounts.videos
                self.isRefreshing = false
            }
        }
    }
}

#Preview {
    StorageUsageView()
        .padding()
        .frame(width: 350)
}
