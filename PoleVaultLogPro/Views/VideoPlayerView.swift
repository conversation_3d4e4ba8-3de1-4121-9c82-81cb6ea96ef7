import SwiftUI
import AVKit
import PhotosUI
import Photos

/// A view that plays a video or displays an image
struct VideoPlayerView: View {
    /// The media item to play
    var media: JumpMediaStruct?

    @State private var player: AVPlayer?
    @State private var isLoading = true
    @State private var errorMessage: String?
    @State private var isImage = false
    @State private var image: UIImage?
    @State private var loadAttempts = 0
    @State private var isRetrying = false
    @State private var maxRetries = 3

    var body: some View {
        ZStack {
            if isImage, let image = image {
                Image(uiImage: image)
                    .resizable()
                    .scaledToFit()
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else if let player = player {
                ZStack {
                    VideoPlayer(player: player)
                        .onDisappear {
                            player.pause()
                        }
                        .frame(maxWidth: .infinity, maxHeight: .infinity)

                    // Play button overlay
                    Button(action: {
                        player.play()
                    }) {
                        ZStack {
                            Circle()
                                .fill(Color.black.opacity(0.5))
                                .frame(width: 80, height: 80)

                            Image(systemName: "play.fill")
                                .font(.system(size: 30))
                                .foregroundColor(.white)
                        }
                    }
                }
            } else if let errorMessage = errorMessage {
                VStack {
                    Image(AppTheme.poleVaultIconName)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: 60, height: 60)
                        .opacity(0.7)
                        .padding()

                    Text("Error loading media")
                        .font(.headline)

                    Text(errorMessage)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .padding()

                    if loadAttempts < maxRetries {
                        Button(isRetrying ? "Retrying..." : "Retry") {
                            isRetrying = true
                            loadAttempts += 1

                            // Add a small delay before retrying to allow any resources to be released
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                                loadMedia()
                                isRetrying = false
                            }
                        }
                        .disabled(isRetrying)
                        .padding()
                        .background(isRetrying ? Color.gray : AppTheme.accentColor)
                        .foregroundColor(.white)
                        .cornerRadius(8)
                        .padding(.top)
                    } else {
                        Text("Maximum retry attempts reached")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .padding(.top)
                    }
                }
            } else if isLoading {
                ProgressView()
                    .scaleEffect(1.5)
            }
        }
        .onAppear {
            loadMedia()
        }
        .onDisappear {
            // Clean up resources when view disappears
            player?.pause()
            player = nil
            image = nil
        }
    }

    private func loadMedia() {
        // Reset state
        isLoading = true
        errorMessage = nil
        player = nil
        isImage = false
        image = nil

        // If this is a retry, add a small delay to allow resources to be released
        let delay: TimeInterval = isRetrying ? 1.0 : 0.0

        DispatchQueue.main.asyncAfter(deadline: .now() + delay) {
            Task {
                await self.loadMediaWithTask()
            }
        }
    }

    private func loadMediaWithTask() async {
        // Check if we have a JumpMedia object
        guard let media = media else {
            DispatchQueue.main.async {
                self.isLoading = false
                self.errorMessage = "No media provided"
            }
            print("Error: No media provided")
            return
        }

        print("Loading media from JumpMedia: \(media.id)")

        // Validate the media first
        let (isAvailable, repairedMedia) = media.validateMedia()

        // If media is not available, show error
        if !isAvailable {
            DispatchQueue.main.async {
                self.errorMessage = "Media not found. It may have been deleted or is no longer accessible."
                self.isLoading = false
            }
            return
        }

        // Use repaired media if available
        let mediaToLoad = repairedMedia ?? media

        // Determine if it's an image or video
        DispatchQueue.main.async {
            self.isImage = mediaToLoad.type == .photo
        }

        if mediaToLoad.type == .photo {
            loadPhotoFromJumpMedia(mediaToLoad)
        } else {
            // Pre-check if the file exists before attempting to load it
            // We no longer support local file URLs, only asset identifiers
            loadVideoFromJumpMedia(mediaToLoad)
        }
    }

    /// Loads a photo from a JumpMediaStruct object
    private func loadPhotoFromJumpMedia(_ media: JumpMediaStruct) {
        Task {
            do {
                // APPROACH 1: Try to load from PHAsset first - this is the preferred approach
                // as it avoids unnecessary local copies
                if let assetIdentifier = media.assetIdentifier {
                    print("Loading photo from PHAsset identifier: \(assetIdentifier)")
                    let fetchResult = PHAsset.fetchAssets(withLocalIdentifiers: [assetIdentifier], options: nil)

                    if let asset = fetchResult.firstObject, asset.mediaType == .image {
                        print("Found valid image asset in Photos library")

                        // Use PHCachingImageManager for better performance
                        let options = PHImageRequestOptions()
                        options.version = .current
                        options.deliveryMode = .opportunistic // Use whatever is available fastest
                        options.isNetworkAccessAllowed = true
                        options.resizeMode = .exact

                        // Request the image at full size
                        let image = await withCheckedContinuation { (continuation: CheckedContinuation<UIImage?, Never>) in
                            PHImageManager.default().requestImage(
                                for: asset,
                                targetSize: PHImageManagerMaximumSize,
                                contentMode: .aspectFit,
                                options: options
                            ) { result, info in
                                // Check for cancellation or errors
                                if let cancelled = info?[PHImageCancelledKey] as? Bool, cancelled {
                                    print("Image request was cancelled")
                                    continuation.resume(returning: nil)
                                    return
                                }

                                if let error = info?[PHImageErrorKey] {
                                    print("Error loading image: \(error)")
                                    continuation.resume(returning: nil)
                                    return
                                }

                                continuation.resume(returning: result)
                            }
                        }

                        if let image = image {
                            print("Successfully loaded image from PHAsset")
                            DispatchQueue.main.async {
                                self.image = image
                                self.isLoading = false
                            }
                            return
                        } else {
                            print("Failed to load image from PHAsset")
                        }
                    }
                }

                // We no longer support local file URLs, only asset identifiers

                // If we couldn't load the image, throw an error
                throw NSError(domain: "VideoPlayerView", code: 404, userInfo: [NSLocalizedDescriptionKey: "Image not found or not loadable"])
            } catch {
                print("Error loading image: \(error.localizedDescription)")
                DispatchQueue.main.async {
                    self.isLoading = false
                    self.errorMessage = error.localizedDescription
                }
            }
        }
    }

    /// Loads a video from a JumpMediaStruct object
    private func loadVideoFromJumpMedia(_ media: JumpMediaStruct) {
        Task {
            do {
                // Only try to load from PHAsset if we have an identifier
                if let assetIdentifier = media.assetIdentifier {
                    print("Loading video from PHAsset identifier: \(assetIdentifier)")
                    // First try to find the asset as a shared asset
                    var asset = SharedMediaManager.shared.fetchSharedAsset(with: assetIdentifier)

                    // If not found as a shared asset, try as a local asset
                    if asset == nil {
                        let fetchResult = PHAsset.fetchAssets(withLocalIdentifiers: [assetIdentifier], options: nil)
                        asset = fetchResult.firstObject
                    }

                    if let asset = asset, asset.mediaType == .video {
                        print("Found valid video asset in Photos library")

                        // Request a player item directly from PHImageManager
                        let options = PHVideoRequestOptions()
                        options.version = .current
                        options.deliveryMode = .highQualityFormat
                        options.isNetworkAccessAllowed = true

                        let playerItem = await withCheckedContinuation { (continuation: CheckedContinuation<AVPlayerItem?, Never>) in
                            PHImageManager.default().requestPlayerItem(
                                forVideo: asset,
                                options: options
                            ) { playerItem, info in
                                continuation.resume(returning: playerItem)
                            }
                        }

                        if let playerItem = playerItem {
                            print("Successfully created player item from PHAsset")

                            // Add an observer for the playerItem status
                            let observation = NotificationCenter.default.addObserver(
                                forName: .AVPlayerItemFailedToPlayToEndTime,
                                object: playerItem,
                                queue: .main
                            ) { [self] notification in
                                if let error = notification.userInfo?[AVPlayerItemFailedToPlayToEndTimeErrorKey] as? Error {
                                    print("Player item failed to play: \(error)")
                                    self.errorMessage = "Failed to play video: \(error.localizedDescription)"
                                    self.isLoading = false
                                }
                            }

                            DispatchQueue.main.async {
                                self.player = AVPlayer(playerItem: playerItem)

                                // Set the playback time if specified
                                if let posterTime = media.posterTime, posterTime > 0 {
                                    self.player?.seek(to: CMTime(seconds: posterTime, preferredTimescale: 600))
                                }

                                // Don't start playing automatically
                                self.isLoading = false

                                // Remove the observer after a delay
                                DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) {
                                    NotificationCenter.default.removeObserver(observation)
                                }
                            }
                            return
                        } else {
                            print("Failed to create player item from PHAsset")
                            throw NSError(domain: "VideoPlayerView", code: 500,
                                         userInfo: [NSLocalizedDescriptionKey: "Failed to create player for video"])
                        }
                    } else {
                        print("No valid video asset found in Photos library with ID: \(assetIdentifier)")
                        throw NSError(domain: "VideoPlayerView", code: 404,
                                     userInfo: [NSLocalizedDescriptionKey: "Video not found in Photos library"])
                    }
                } else {
                    // We no longer support local file URLs, only asset identifiers
                    print("No asset identifier available for video")
                    throw NSError(domain: "VideoPlayerView", code: 404,
                                 userInfo: [NSLocalizedDescriptionKey: "Video not found"])
                }

            } catch {
                print("Error loading video: \(error.localizedDescription)")
                DispatchQueue.main.async {
                    self.isLoading = false
                    self.errorMessage = "Error loading video: \(error.localizedDescription)"
                }
            }
        }
    }

    // Legacy methods removed as we're using the new media browser
}

/// A view that displays a photo
struct PhotoView: View {
    /// The media item to display
    let media: JumpMediaStruct

    /// The photo to display
    @State private var photo: UIImage?

    /// Whether the photo is being loaded
    @State private var isLoading = true

    /// Any error that occurred during loading
    @State private var error: Error?

    var body: some View {
        ZStack {
            if let photo = photo {
                Image(uiImage: photo)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .edgesIgnoringSafeArea(.all)
            } else if isLoading {
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle())
                    .foregroundColor(.white)
            } else if let error = error {
                VStack(spacing: 8) {
                    Image(systemName: "exclamationmark.triangle")
                        .font(.largeTitle)
                        .foregroundColor(.orange)

                    Text("Error loading photo")
                        .font(.headline)
                        .foregroundColor(.white)

                    Text(error.localizedDescription)
                        .font(.caption)
                        .foregroundColor(.gray)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)

                    Button(action: {
                        // Reset error and try again
                        self.error = nil
                        self.loadPhoto()
                    }) {
                        Text("Try Again")
                            .font(.caption)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(AppTheme.accentColor)
                            .foregroundColor(.white)
                            .cornerRadius(8)
                    }
                    .padding(.top, 8)
                }
            }
        }
        .onAppear {
            loadPhoto()
        }
    }

    /// Loads the photo
    private func loadPhoto() {
        isLoading = true
        error = nil

        Task {
            do {
                // Only try to load from PHAsset if we have an identifier
                if let assetIdentifier = media.assetIdentifier {
                    print("Loading photo from PHAsset: \(assetIdentifier)")
                    // First try to find the asset as a shared asset
                    var asset = SharedMediaManager.shared.fetchSharedAsset(with: assetIdentifier)

                    // If not found as a shared asset, try as a local asset
                    if asset == nil {
                        let fetchResult = PHAsset.fetchAssets(withLocalIdentifiers: [assetIdentifier], options: nil)
                        asset = fetchResult.firstObject
                    }

                    if let asset = asset, asset.mediaType == .image {
                        print("Found valid image asset in Photos library")

                        // Use PHImageManager to request the image
                        let options = PHImageRequestOptions()
                        options.isNetworkAccessAllowed = true     // pull from iCloud if needed
                        options.deliveryMode = .highQualityFormat

                        // Request full-size image
                        let image: UIImage? = await withCheckedContinuation { continuation in
                            PHImageManager.default().requestImage(
                                for: asset,
                                targetSize: PHImageManagerMaximumSize,
                                contentMode: .aspectFit,
                                options: options
                            ) { image, info in
                                if let cancelled = info?[PHImageCancelledKey] as? Bool, cancelled {
                                    print("Image request was cancelled")
                                    continuation.resume(returning: nil)
                                    return
                                }

                                if let error = info?[PHImageErrorKey] {
                                    print("Error loading image: \(error)")
                                    continuation.resume(returning: nil)
                                    return
                                }

                                continuation.resume(returning: image)
                            }
                        }

                        if let image = image {
                            print("Successfully loaded image from PHAsset")
                            DispatchQueue.main.async {
                                self.photo = image
                                self.isLoading = false
                            }
                            return
                        } else {
                            print("Failed to load image from PHAsset")
                            throw NSError(domain: "PhotoView", code: 500,
                                         userInfo: [NSLocalizedDescriptionKey: "Failed to load image from Photos library"])
                        }
                    } else {
                        print("No valid image asset found in Photos library with ID: \(assetIdentifier)")
                        throw NSError(domain: "PhotoView", code: 404,
                                     userInfo: [NSLocalizedDescriptionKey: "Image not found in Photos library"])
                    }
                } else {
                    // We no longer support local file URLs, only asset identifiers
                    print("No asset identifier available for image")
                    throw NSError(domain: "PhotoView", code: 404,
                                 userInfo: [NSLocalizedDescriptionKey: "Image not found"])
                }
            } catch {
                print("Error loading photo: \(error.localizedDescription)")
                DispatchQueue.main.async {
                    self.error = error
                    self.isLoading = false
                }
            }
        }
    }


}

#Preview {
    VStack(spacing: 20) {

        // New JumpMedia video
        VideoPlayerView(
            media: JumpMediaStruct(
                type: .video,
                assetIdentifier: nil,

                posterTime: 0.5
            )
        )
        .frame(height: 200)

        // New JumpMedia photo
        PhotoView(
            media: JumpMediaStruct(
                type: .photo,
                assetIdentifier: nil,

            )
        )
        .frame(height: 200)
    }
    .padding()
}
