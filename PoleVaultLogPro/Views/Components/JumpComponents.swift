import SwiftUI
import CoreData

// MARK: - Jump-specific Components

/// A reusable delete section for jump editing
struct JumpDeleteSection: View {
    @Binding var showingDeleteAlert: Bool

    var body: some View {
        Section {
            Button(action: {
                showingDeleteAlert = true
            }) {
                HStack {
                    Image(systemName: "trash")
                    Text("Delete Jump")
                }
                .foregroundColor(.red)
            }
        }
    }
}

/// A reusable jump statistics display
struct JumpStatsDisplay: View {
    let made: Int
    let missed: Int
    let passed: Int

    var body: some View {
        HStack(spacing: 12) {
            StatItem(label: "Made", value: made, color: .green)
            StatItem(label: "Miss", value: missed, color: .red)
            StatItem(label: "Pass", value: passed, color: .orange)
        }
    }

    private struct StatItem: View {
        let label: String
        let value: Int
        let color: Color

        var body: some View {
            VStack(spacing: 2) {
                Text("\(value)")
                    .font(.headline)
                    .foregroundColor(color)
                Text(label)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
}

/// A reusable jump result indicator
struct JumpResultIndicator: View {
    let result: String

    var body: some View {
        Circle()
            .fill(resultColor)
            .frame(width: 12, height: 12)
    }

    private var resultColor: Color {
        switch result.lowercased() {
        case "make":
            return .green
        case "miss":
            return .red
        case "pass":
            return .orange
        default:
            return .gray
        }
    }
}

/// A reusable height display component
struct HeightDisplay: View {
    let heightCm: Double
    let style: HeightDisplayStyle

    enum HeightDisplayStyle {
        case compact
        case detailed
        case twoLine
    }

    var body: some View {
        switch style {
        case .compact:
            Text(HeightConverter.formatHeight(cm: heightCm))
                .font(.subheadline)
        case .detailed:
            let format = HeightConverter.formatHeightTwoLine(cm: heightCm)
            VStack(alignment: .trailing) {
                Text(format.primary)
                    .font(.headline)
                if let secondary = format.secondary {
                    Text(secondary)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        case .twoLine:
            let format = HeightConverter.formatHeightTwoLine(cm: heightCm)
            VStack(alignment: .leading, spacing: 2) {
                Text(format.primary)
                    .font(.subheadline)
                    .fontWeight(.medium)
                if let secondary = format.secondary {
                    Text(secondary)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
    }
}

/// A reusable attempt number display
struct AttemptDisplay: View {
    let attemptIndex: Int16

    var body: some View {
        Text("#\(attemptIndex)")
            .font(.caption)
            .padding(.horizontal, 6)
            .padding(.vertical, 2)
            .background(Color.secondary.opacity(0.2))
            .cornerRadius(4)
    }
}

/// A reusable pole display component
struct PoleDisplay: View {
    let pole: Pole?
    let style: PoleDisplayStyle

    enum PoleDisplayStyle {
        case name
        case nameAndLength
        case compact
    }

    var body: some View {
        if let pole = pole {
            switch style {
            case .name:
                Text(pole.getDisplayName())
            case .nameAndLength:
                VStack(alignment: .leading, spacing: 2) {
                    Text(pole.getDisplayName())
                        .font(.subheadline)
                    Text(HeightConverter.formatHeight(cm: pole.lengthCm))
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            case .compact:
                Text(pole.getDisplayName())
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        } else {
            Text("No pole")
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
}

/// A reusable jump summary card
struct JumpSummaryCard: View {
    let jump: Jump
    let showMedia: Bool
    let onTap: (() -> Void)?

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                HeightDisplay(heightCm: jump.barHeightCm, style: .twoLine)
                Spacer()
                VStack(alignment: .trailing, spacing: 4) {
                    JumpResultIndicator(result: jump.result ?? "miss")
                    AttemptDisplay(attemptIndex: jump.attemptIndex)
                }
            }

            if let pole = jump.pole {
                PoleDisplay(pole: pole, style: .compact)
            }

            if showMedia, let mediaItems = jump.mediaItems, mediaItems.count > 0 {
                MediaIndicator(hasMedia: true, mediaCount: mediaItems.count)
            }

            if let comment = jump.comment, !comment.isEmpty {
                Text(comment)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(AppTheme.cornerRadius)
        .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
        .onTapGesture {
            onTap?()
        }
    }
}
