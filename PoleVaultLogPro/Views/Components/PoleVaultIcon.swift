import SwiftUI

/// A view that displays the appropriate pole vault icon based on the current theme and color scheme
struct PoleVaultIcon: View {
    @Environment(\.colorScheme) private var colorScheme
    
    var size: CGSize = CGSize(width: 24, height: 24)
    var foregroundColor: Color? = nil
    
    var body: some View {
        Image(iconName)
            .resizable()
            .aspectRatio(contentMode: .fit)
            .frame(width: size.width, height: size.height)
            .foregroundStyle(foregroundColor ?? .primary)
    }
    
    private var iconName: String {
        colorScheme == .dark ? AppTheme.poleVaultIconNameDark : AppTheme.poleVaultIconName
    }
}

#Preview {
    VStack(spacing: 20) {
        HStack(spacing: 20) {
            PoleVaultIcon(size: CGSize(width: 50, height: 50))
                .environment(\.colorScheme, .light)
            
            PoleVaultIcon(size: CGSize(width: 50, height: 50))
                .environment(\.colorScheme, .dark)
        }
        .padding()
        .background(Color.gray.opacity(0.2))
        .cornerRadius(8)
        
        HStack(spacing: 20) {
            PoleVaultIcon(size: CGSize(width: 50, height: 50), foregroundColor: .blue)
                .environment(\.colorScheme, .light)
            
            PoleVaultIcon(size: CGSize(width: 50, height: 50), foregroundColor: .blue)
                .environment(\.colorScheme, .dark)
        }
        .padding()
        .background(Color.gray.opacity(0.2))
        .cornerRadius(8)
    }
    .padding()
}
