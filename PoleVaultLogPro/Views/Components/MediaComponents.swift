import SwiftUI
import PhotosUI
import CoreData

// MARK: - Media Section Components

/// A reusable media section for new jumps with direct PhotosPicker integration
struct MediaSectionNew: View {
    @Binding var selectedMediaIds: [String]
    @Binding var mediaStructs: [JumpMediaStruct]
    @State private var selectedItems: [PhotosPickerItem] = []
    @State private var isLoading = false

    var body: some View {
        Section(header: Text("Media")) {
            // Add Photos/Videos button with direct PhotosPicker
            PhotosPicker(
                selection: $selectedItems,
                matching: .any(of: [.images, .videos]),
                photoLibrary: .shared()
            ) {
                Label("Add Photos/Videos", systemImage: "plus.circle")
            }
            .onChange(of: selectedItems) { oldValue, newValue in
                processSelectedItems()
            }

            if isLoading {
                HStack {
                    ProgressView()
                        .scaleEffect(0.8)
                    Text("Processing media...")
                        .foregroundColor(.secondary)
                }
                .padding(.vertical, 4)
            }

            // Show media thumbnails
            if !mediaStructs.isEmpty {
                LazyVGrid(columns: [
                    GridItem(.adaptive(minimum: 80, maximum: 80), spacing: 8)
                ], spacing: 8) {
                    ForEach(mediaStructs, id: \.id) { media in
                        MediaThumbnailWithControls(
                            media: media,
                            onDelete: { deleteMedia(media) },
                            onTap: { /* Preview functionality can be added later */ }
                        )
                    }
                }
                .padding(.vertical, 4)
            }
        }
    }

    private func processSelectedItems() {
        guard !selectedItems.isEmpty else { return }

        isLoading = true

        Task {
            var newMediaStructs: [JumpMediaStruct] = []
            var newMediaIds: [String] = []

            for item in selectedItems {
                if let media = await MediaStorageManager.shared.processMediaItem(item),
                   let assetId = media.assetIdentifier {
                    newMediaStructs.append(media)
                    newMediaIds.append(assetId)
                }
            }

            DispatchQueue.main.async {
                selectedMediaIds.append(contentsOf: newMediaIds)
                mediaStructs.append(contentsOf: newMediaStructs)
                isLoading = false
                selectedItems = []
            }
        }
    }

    private func deleteMedia(_ media: JumpMediaStruct) {
        if let assetId = media.assetIdentifier,
           let index = selectedMediaIds.firstIndex(of: assetId) {
            selectedMediaIds.remove(at: index)
        }

        if let index = mediaStructs.firstIndex(where: { $0.id == media.id }) {
            mediaStructs.remove(at: index)
        }
    }
}

/// A reusable media section for editing existing jumps with direct PhotosPicker integration
struct MediaSectionEdit: View {
    @Environment(\.managedObjectContext) private var viewContext
    let jump: Jump
    @State private var selectedItems: [PhotosPickerItem] = []
    @State private var isLoading = false
    @State private var mediaStructs: [JumpMediaStruct] = []

    var body: some View {
        Section(header: Text("Media")) {
            // Add Photos/Videos button with direct PhotosPicker
            PhotosPicker(
                selection: $selectedItems,
                matching: .any(of: [.images, .videos]),
                photoLibrary: .shared()
            ) {
                Label("Add Photos/Videos", systemImage: "plus.circle")
            }
            .onChange(of: selectedItems) { oldValue, newValue in
                processSelectedItems()
            }

            if isLoading {
                HStack {
                    ProgressView()
                        .scaleEffect(0.8)
                    Text("Processing media...")
                        .foregroundColor(.secondary)
                }
                .padding(.vertical, 4)
            }

            // Show media thumbnails
            if !mediaStructs.isEmpty {
                LazyVGrid(columns: [
                    GridItem(.adaptive(minimum: 80, maximum: 80), spacing: 8)
                ], spacing: 8) {
                    ForEach(mediaStructs, id: \.id) { media in
                        MediaThumbnailWithControls(
                            media: media,
                            onDelete: { deleteMedia(media) },
                            onTap: { /* Preview functionality can be added later */ }
                        )
                    }
                }
                .padding(.vertical, 4)
            }
        }
        .onAppear {
            loadExistingMedia()
        }
    }

    private func loadExistingMedia() {
        mediaStructs = jump.getMediaItemsAsStructs()
    }

    private func processSelectedItems() {
        guard !selectedItems.isEmpty else { return }

        isLoading = true

        Task {
            for item in selectedItems {
                if let media = await MediaStorageManager.shared.processMediaItem(item) {
                    DispatchQueue.main.async {
                        let success = jump.addMediaStruct(media)

                        if success {
                            do {
                                try viewContext.save()
                                loadExistingMedia() // Reload to show new media
                            } catch {
                                print("Error saving jump with new media: \(error)")
                            }
                        }
                    }
                }
            }

            DispatchQueue.main.async {
                isLoading = false
                selectedItems = []
            }
        }
    }

    private func deleteMedia(_ media: JumpMediaStruct) {
        let success = jump.removeMedia(withId: media.id)

        if success {
            do {
                try viewContext.save()
                loadExistingMedia() // Reload to reflect deletion
            } catch {
                print("Error removing media from jump: \(error)")
            }
        }
    }
}

// MARK: - Sheet Presentation Models
struct MediaPreviewSheetItem: Identifiable {
    let id = UUID()
    let media: JumpMediaStruct
}

/// A thumbnail view with delete and tap controls
struct MediaThumbnailWithControls: View {
    let media: JumpMediaStruct
    let onDelete: () -> Void
    let onTap: () -> Void

    @State private var previewSheet: MediaPreviewSheetItem?

    var body: some View {
        ZStack(alignment: .topTrailing) {
            // Thumbnail with tap gesture
            Button(action: {
                previewSheet = MediaPreviewSheetItem(media: media)
            }) {
                MediaThumbnailView(
                    media: media,
                    size: CGSize(width: 80, height: 80),
                    showTypeIcon: false // Don't show the built-in play icon
                )
                .clipShape(RoundedRectangle(cornerRadius: 8))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                )
            }
            .buttonStyle(PlainButtonStyle())

            // Custom video play indicator (only for videos)
            if media.type == .video {
                Image(systemName: "play.circle.fill")
                    .foregroundColor(.white)
                    .font(.system(size: 24))
                    .shadow(color: .black.opacity(0.5), radius: 2)
                    .allowsHitTesting(false)
            }

            // Delete button overlay - positioned outside the thumbnail area
            Button(action: onDelete) {
                Image(systemName: "xmark.circle.fill")
                    .foregroundColor(.red)
                    .background(Color.white, in: Circle())
                    .font(.system(size: 20))
            }
            .buttonStyle(PlainButtonStyle())
            .offset(x: 8, y: -8)
            .zIndex(10) // Ensure delete button is always on top
        }
        .frame(width: 88, height: 88) // Extra space for delete button
        .sheet(item: $previewSheet) { item in
            MediaPreviewSheet(
                media: item.media,
                isPresented: Binding(
                    get: { previewSheet != nil },
                    set: { if !$0 { previewSheet = nil } }
                ),
                onDelete: {
                    previewSheet = nil
                    // Add a small delay to ensure sheet dismisses before deleting
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        onDelete()
                    }
                }
            )
        }
    }
}

/// A reusable media indicator component
struct MediaIndicator: View {
    let hasMedia: Bool
    let mediaCount: Int

    var body: some View {
        if hasMedia {
            HStack(spacing: 2) {
                Image(systemName: "photo")
                    .font(.caption)
                Text("\(mediaCount)")
                    .font(.caption)
            }
            .foregroundColor(.secondary)
        }
    }
}
