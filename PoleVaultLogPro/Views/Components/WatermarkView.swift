import SwiftUI

struct WatermarkView: View {
    var opacity: Double = 0.05
    var scale: CGFloat = 1.0

    var body: some View {
        GeometryReader { geometry in
            PoleVaultIcon(
                size: CGSize(width: geometry.size.width * scale, height: geometry.size.width * scale),
                foregroundColor: AppTheme.accentColor
            )
            .position(x: geometry.size.width / 2, y: geometry.size.height / 2)
            .opacity(opacity)
        }
        .edgesIgnoringSafeArea(.all)
    }
}

#Preview {
    ZStack {
        Color(.systemBackground).edgesIgnoringSafeArea(.all)
        WatermarkView()
        Text("Content goes here")
            .font(.title)
    }
}
