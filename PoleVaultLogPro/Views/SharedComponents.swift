import SwiftUI

// Custom label style to make labels more compact
struct CompactLabelStyle: LabelStyle {
    func makeBody(configuration: Configuration) -> some View {
        HStack(spacing: 2) {
            configuration.icon
            configuration.title
        }
    }
}

// Shared measurement slider component
struct MeasurementSlider: View {
    let title: String
    @Binding var value: Double
    let range: ClosedRange<Double>
    let step: Double
    let unit: String

    private var useMetric: Bool { AppTheme.useMetricSystem }

    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                Text(title)
                Spacer()
                Text(formattedValue)
                    .foregroundColor(.secondary)
            }

            Slider(value: $value, in: range, step: step)
                .tint(AppTheme.accentColor)
        }
    }

    private var formattedValue: String {
        if unit == "ft" {
            return HeightConverter.formatStepMeasurement(cm: value)
        } else if unit == "in" {
            if title == "Standard" {
                return HeightConverter.formatStandardMeasurement(cm: value)
            } else {
                let inches = value / 2.54
                return String(format: "%.0f in", inches)
            }
        } else {
            return "\(Int(value)) \(unit)"
        }
    }
}
