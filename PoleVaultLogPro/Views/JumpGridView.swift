import SwiftUI
import CoreData
import UIKit

// MARK: - Sheet Presentation Models
struct HeightSheetItem: Identifiable {
    let id = UUID()
    let height: Double
    let isAddNew: Bool

    init(height: Double, isAddNew: Bool = false) {
        self.height = height
        self.isAddNew = isAddNew
    }
}

struct EditHeightSheetItem: Identifiable {
    let id = UUID()
    let height: Double
}

struct JumpGridView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @ObservedObject var session: Session
    @StateObject private var viewModel: SessionViewModel

    // Navigation callbacks
    let onNavigateToNewJump: ((Double, Int16) -> Void)?
    let onNavigateToEditJump: ((String) -> Void)?

    @State private var heightSheet: HeightSheetItem?
    @State private var editHeightSheet: EditHeightSheetItem?
    @State private var selectedHeight: Double = 0
    @State private var selectedAttemptIndex: Int16 = 1
    @State private var selectedJump: Jump?
    @State private var selectedHeightIndex: Int? = nil
    @State private var newHeightValue: Double = 300 // Default to 300cm (about 9'10")

    // Navigation state for jump editing/creation
    @State private var newJumpHeight: Double?
    @State private var newJumpAttempt: Int16?

    // For haptic feedback
    private let lightImpact = UIImpactFeedbackGenerator(style: .light)
    private let mediumImpact = UIImpactFeedbackGenerator(style: .medium)
    private let heavyImpact = UIImpactFeedbackGenerator(style: .heavy)

    // Fetch jumps for this session with explicit entity description
    @FetchRequest private var jumps: FetchedResults<Jump>

    init(session: Session, onNavigateToNewJump: ((Double, Int16) -> Void)? = nil, onNavigateToEditJump: ((String) -> Void)? = nil) {
        self.session = session
        self.onNavigateToNewJump = onNavigateToNewJump
        self.onNavigateToEditJump = onNavigateToEditJump

        // Initialize the view model
        _viewModel = StateObject(wrappedValue: SessionViewModel(session: session))

        // Create a fetch request for jumps in this session with explicit entity description
        _jumps = FetchRequest(fetchRequest: CoreDataFetchRequestHelper.createJumpFetchRequest(
            context: PersistenceController.shared.container.viewContext,
            sortDescriptors: [
                NSSortDescriptor(keyPath: \Jump.barHeightCm, ascending: true),
                NSSortDescriptor(keyPath: \Jump.attemptIndex, ascending: true)
            ],
            predicate: NSPredicate(format: "session == %@", session)
        ))

        // Set default newHeightValue based on session's heights
        if let firstHeight = session.getGridHeights().first {
            // Add 6 inches (15.24cm) for practice sessions
            if session.type?.lowercased() == "practice" {
                newHeightValue = firstHeight + 15.24 // 6 inches in cm
            } else {
                newHeightValue = firstHeight + 5 // Keep 5cm for meets
            }
        } else {
            newHeightValue = 300
        }
    }

    var body: some View {
        VStack(spacing: 0) {
                // Use a single ScrollView for synchronized scrolling
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(alignment: .top, spacing: 0) {
                        // Left column with headers
                        VStack(alignment: .center, spacing: 0) {
                            // Top-left corner with add button
                            ZStack(alignment: .leading) {
                                Rectangle()
                                    .fill(Color.clear)
                                    .frame(width: 100, height: 50)

                                Button(action: {
                                    // Show add height sheet with a value 6 inches (15.24cm) higher than the first height (most recent)
                                    if let firstHeight = session.getGridHeights().first {
                                        // Add 6 inches (15.24cm) for practice sessions
                                        if session.type?.lowercased() == "practice" {
                                            newHeightValue = firstHeight + 15.24 // 6 inches in cm
                                        } else {
                                            newHeightValue = firstHeight + 5 // Keep 5cm for meets
                                        }
                                    } else {
                                        newHeightValue = 300
                                    }

                                    // Show add height sheet
                                    heightSheet = HeightSheetItem(height: newHeightValue, isAddNew: true)
                                }) {
                                    Image(systemName: "plus.circle.fill")
                                        .font(.title2)
                                        .foregroundColor(AppTheme.accentColor)
                                }
                                .padding(.leading, 16)
                            }

                        Divider()

                        // Row headers
                        ForEach(1...3, id: \.self) { attemptIndex in
                            Text("Jump \(attemptIndex)")
                                .font(.caption)
                                .fontWeight(.medium)
                                .frame(width: 100, height: 60, alignment: .center)
                        }
                    }
                    .background(Color(.systemBackground))

                    // Right columns with heights and grid cells
                    VStack(spacing: 0) {
                        // Height headers row
                        HStack(alignment: .bottom, spacing: 0) {
                            // Height headers
                            ForEach(session.getGridHeights(), id: \.self) { height in
                                Button(action: {
                                    // Show height options sheet
                                    heightSheet = HeightSheetItem(height: height, isAddNew: false)
                                }) {
                                    VStack(alignment: .center, spacing: 2) {
                                        let heightFormat = HeightConverter.formatHeightTwoLine(cm: height)

                                        // Primary unit at the top
                                        Text(heightFormat.primary)
                                            .font(.caption)
                                            .fontWeight(.bold)
                                            .frame(width: 100, alignment: .center)

                                        // Secondary unit below (if enabled)
                                        if let secondaryUnit = heightFormat.secondary {
                                            Text(secondaryUnit)
                                                .font(.caption2)
                                                .foregroundColor(.secondary)
                                                .frame(width: 100, alignment: .center)
                                        }
                                    }
                                    .frame(width: 100, height: 50)
                                    .background(Color(.systemBackground))
                                }
                                .buttonStyle(BorderlessButtonStyle())
                                .contextMenu {
                                    Button(action: {
                                        // Delete height column
                                        deleteHeightColumn(height)
                                    }) {
                                        Label("Delete Height", systemImage: "trash")
                                    }
                                }
                                .onLongPressGesture {
                                    // Show options for this height
                                    heightSheet = HeightSheetItem(height: height, isAddNew: false)
                                }
                            }
                        }

                        Divider()

                        // Grid cells rows
                        HStack(alignment: .top, spacing: 0) {
                            // Grid cells by height
                            ForEach(session.getGridHeights(), id: \.self) { height in
                                VStack(spacing: 0) {
                                    ForEach(1...3, id: \.self) { attemptIndex in
                                        let jump = findJump(height: height, attemptIndex: Int16(attemptIndex))

                                        GridCell(
                                            jump: jump,
                                            height: height,
                                            attemptIndex: Int16(attemptIndex),
                                            viewModel: viewModel,
                                            onTap: {


                                                // Get the index of this height in the grid
                                                let heights = session.getGridHeights()
                                                if let index = heights.firstIndex(of: height) {
                                                    selectedHeightIndex = index
                                                }

                                                // Call the cellTapped method with the correct attempt index
                                                let correctAttemptIndex = Int16(attemptIndex)
                                                cellTapped(height: height, attemptIndex: correctAttemptIndex, jump: jump)
                                            }
                                        )
                                        .id("\(height)-\(attemptIndex)-\(jump?.id ?? "empty")")
                                        .frame(width: 100, height: 60)
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // Summary bar
            summaryBar

            // Empty view for spacing
            EmptyView()
        }
        .id(viewModel.id) // Add an ID to preserve state across navigation
        .sheet(item: $heightSheet) { item in
            if item.isAddNew {
                // Add new height sheet
                AddHeightSheet(
                    initialHeight: item.height,
                    sessionType: session.type ?? "practice",
                    heightStandard: session.heightStandard,
                    onAdd: { height in
                        addHeightColumn(height)
                        heightSheet = nil
                    }
                )
            } else {
                // Height options sheet
                HeightEditSheet(
                    height: item.height,
                    onDelete: {
                        deleteHeightColumn(item.height)
                        heightSheet = nil
                    },
                    onDuplicate: {
                        duplicateHeightColumn(item.height)
                        heightSheet = nil
                    },
                    onEdit: {
                        // Store the height to edit
                        let heightToEdit = item.height

                        // First dismiss the current sheet
                        heightSheet = nil

                        // Show edit height sheet after a short delay to ensure proper transition
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                            editHeightSheet = EditHeightSheetItem(height: heightToEdit)
                        }
                    },
                    onAddNew: {
                        // First dismiss the current sheet
                        heightSheet = nil

                        // Calculate the new height value based on the first height
                        var newHeight: Double
                        if let firstHeight = session.getGridHeights().first {
                            // Add 6 inches (15.24cm) for practice sessions
                            if session.type?.lowercased() == "practice" {
                                newHeight = firstHeight + 15.24 // 6 inches in cm
                            } else {
                                newHeight = firstHeight + 5 // Keep 5cm for meets
                            }
                        } else {
                            newHeight = 300
                        }

                        // Show add height sheet after a short delay to ensure proper transition
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                            heightSheet = HeightSheetItem(height: newHeight, isAddNew: true)
                        }
                    }
                )
            }
        }
        .sheet(item: $editHeightSheet) { item in
            EditHeightSheet(
                originalHeight: item.height,
                sessionType: session.type ?? "practice",
                heightStandard: session.heightStandard,
                onUpdate: { newHeight in
                    updateHeightColumn(oldHeight: item.height, newHeight: newHeight)
                    editHeightSheet = nil
                }
            )
        }
    }

    private var summaryBar: some View {
        HStack(spacing: 16) {
            VStack(alignment: .leading) {
                Text("Attempts \(jumps.count)")
                    .font(.caption)
                    .foregroundColor(.secondary)

                HStack(spacing: 12) {
                    Label("\(jumps.filter { $0.result == "make" }.count)", systemImage: "checkmark.circle.fill")
                        .foregroundColor(.green)

                    Label("\(jumps.filter { $0.result == "miss" }.count)", systemImage: "xmark.circle.fill")
                        .foregroundColor(.red)

                    Label("\(jumps.filter { $0.result == "pass" }.count)", systemImage: "arrow.right.circle.fill")
                        .foregroundColor(.gray)
                }
                .font(.caption)
            }

            Spacer()

            if session.bestHeightCm > 0 {
                VStack(alignment: .trailing) {
                    Text("Best")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    VStack(alignment: .trailing, spacing: 2) {
                        let heightFormat = HeightConverter.formatHeightTwoLine(cm: session.bestHeightCm)

                        // Primary unit at the top
                        Text(heightFormat.primary)
                            .fontWeight(.bold)
                            .foregroundColor(AppTheme.accentColor)

                        // Secondary unit below (if enabled)
                        if let secondaryUnit = heightFormat.secondary {
                            Text(secondaryUnit)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .overlay(
            Rectangle()
                .frame(height: 1)
                .foregroundColor(Color.gray.opacity(0.3)),
            alignment: .top
        )
    }

    // MARK: - Helper Methods

    private func findJump(height: Double, attemptIndex: Int16) -> Jump? {
        return jumps.first { jump in
            abs(jump.barHeightCm - height) < 0.1 && jump.attemptIndex == attemptIndex
        }
    }

    private func cellTapped(height: Double, attemptIndex: Int16, jump: Jump?) {
        lightImpact.impactOccurred()

        // Store the exact height and attempt index that were passed to this method
        let clickedAttemptIndex = attemptIndex

        // For empty cells, we need to ensure we're using the exact height from the column
        var finalHeight = height
        if height < 1.0 {
            // Try to find the correct height from the session's grid heights
            let heights = session.getGridHeights()

            // Try to find the index of this height in the grid
            if let columnIndex = heights.firstIndex(of: height) {
                // If we have a valid height at this index, use it
                if columnIndex < heights.count {
                    let correctHeight = heights[columnIndex]
                    finalHeight = correctHeight
                }
            }
        }

        // Set the state variables for other parts of the UI that might need them
        selectedHeight = finalHeight
        selectedAttemptIndex = clickedAttemptIndex

        // If we have a jump, navigate to edit view
        if let jump = jump {
            // Force a refresh of the jump from Core Data
            viewContext.refresh(jump, mergeChanges: true)

            // Use callback to navigate to edit jump
            onNavigateToEditJump?(jump.id ?? "")
        } else {
            // For new jumps, navigate to new jump view
            newJumpHeight = finalHeight
            newJumpAttempt = clickedAttemptIndex

            // Use callback to navigate to new jump
            onNavigateToNewJump?(finalHeight, clickedAttemptIndex)
        }
    }

    private func addHeightColumn(_ height: Double) {
        // Get the current heights
        let heights = session.getGridHeights()

        // If using imperial, round to the nearest quarter-inch
        let roundedHeight = !AppTheme.useMetricSystem ? HeightConverter.roundToQuarterInchInCm(height) : height

        // Check if this is a meet - only enforce height order rules for meets
        let isMeet = (session.type?.lowercased() == "meet")

        // For meets, check if the new height is higher than the first height (most recent)
        if isMeet, let firstHeight = heights.first, roundedHeight <= firstHeight {
            // Provide haptic feedback for error
            let notificationFeedback = UINotificationFeedbackGenerator()
            notificationFeedback.notificationOccurred(.error)

            // Error: New height must be higher than the most recent height
            return
        }

        // Add the height to the grid (it will be inserted at index 0)
        _ = session.addGridHeight(heightCm: roundedHeight)

        do {
            try viewContext.save()
        } catch {
            // Error handling is silent
        }
    }

    private func deleteHeightColumn(_ height: Double) {
        // Skip heights that are 0 or very close to 0
        if height < 1.0 {
            // Just remove it from the grid without trying to find jumps
            _ = session.removeGridHeight(heightCm: height)
            try? viewContext.save()
            return
        }

        // Check if any of the jumps at this height are misses
        let jumpsAtHeight = jumps.filter({ abs($0.barHeightCm - height) < 0.1 })
        let hasMisses = jumpsAtHeight.contains(where: { $0.result == "miss" })

        // Delete all jumps at this height
        for jump in jumpsAtHeight {
            viewContext.delete(jump)
        }

        // Remove the height from the grid
        // This is an explicit user action to delete a height column,
        // so we should honor it even with our new "keep all heights" policy
        _ = session.removeGridHeight(heightCm: height)

        do {
            try viewContext.save()

            // If we deleted any misses, we need to recalculate the consecutive miss count
            // and potentially reset the out status
            if hasMisses {
                viewModel.resetOutStatus()
            }
        } catch {
            // Error handling is silent
        }
    }

    private func duplicateHeightColumn(_ height: Double) {
        // Create a new height that's 5cm higher
        let newHeight = height + 5

        // If using imperial, round to the nearest quarter-inch
        let roundedNewHeight = !AppTheme.useMetricSystem ? HeightConverter.roundToQuarterInchInCm(newHeight) : newHeight

        // Add the new height to the grid
        _ = session.addGridHeight(heightCm: roundedNewHeight)

        // Copy technical values from the previous height's jumps
        let previousJumps = jumps.filter { abs($0.barHeightCm - height) < 0.1 }

        for jump in previousJumps {
            // Create a new jump with the same technical values but at the new height
            _ = Jump.create(
                in: viewContext,
                session: session,
                order: Int16(jumps.count + 1),
                heightCm: roundedNewHeight,
                result: "miss", // Default to miss for new height
                attemptIndex: jump.attemptIndex,
                runStartCm: jump.runStartCm,
                handHoldCm: jump.handHoldCm,
                takeOffStepCm: jump.takeOffStepCm,
                standardCm: jump.standardCm
            )
        }

        do {
            try viewContext.save()
        } catch {
            print("Error duplicating height column: \(error)")
        }
    }

    private func updateHeightColumn(oldHeight: Double, newHeight: Double) {
        // If using imperial, round to the nearest quarter-inch
        let roundedNewHeight = !AppTheme.useMetricSystem ? HeightConverter.roundToQuarterInchInCm(newHeight) : newHeight

        // Check if this is a meet - only enforce height order rules for meets
        let isMeet = (session.type?.lowercased() == "meet")

        // First check if the new height already exists
        if session.getGridHeights().contains(where: { abs($0 - roundedNewHeight) < 0.1 }) && abs(oldHeight - roundedNewHeight) > 0.1 {
            // Height already exists, don't update
            // Provide haptic feedback for error
            let notificationFeedback = UINotificationFeedbackGenerator()
            notificationFeedback.notificationOccurred(.error)
            return
        }

        // Get the current heights
        let heights = session.getGridHeights()

        // For meets, enforce height order rules
        if isMeet, let index = heights.firstIndex(of: oldHeight) {
            // For the leftmost column (index 0), the new height must be higher than the next column
            if index == 0 && index + 1 < heights.count && roundedNewHeight <= heights[index + 1] {
                // Provide haptic feedback for error
                let notificationFeedback = UINotificationFeedbackGenerator()
                notificationFeedback.notificationOccurred(.error)

                // Show an alert or handle the error
                print("Error: Height must be higher than the next column")
                return
            }

            // For other columns, the new height must be lower than the previous column and higher than the next column
            if index > 0 && roundedNewHeight >= heights[index - 1] {
                // Provide haptic feedback for error
                let notificationFeedback = UINotificationFeedbackGenerator()
                notificationFeedback.notificationOccurred(.error)

                // Show an alert or handle the error
                print("Error: Height must be lower than the previous column")
                return
            }

            if index + 1 < heights.count && roundedNewHeight <= heights[index + 1] {
                // Provide haptic feedback for error
                let notificationFeedback = UINotificationFeedbackGenerator()
                notificationFeedback.notificationOccurred(.error)

                // Show an alert or handle the error
                print("Error: Height must be higher than the next column")
                return
            }
        }

        // Update the height in the grid
        if let success = session.updateGridHeight(oldHeightCm: oldHeight, newHeightCm: roundedNewHeight), success {
            // Update all jumps at this height
            for jump in jumps.filter({ abs($0.barHeightCm - oldHeight) < 0.1 }) {
                jump.barHeightCm = roundedNewHeight
                jump.barHeightIn = roundedNewHeight / 2.54
            }

            do {
                try viewContext.save()
            } catch {
                print("Error updating height column: \(error)")
            }
        }
    }

    /// Get the correct height for a specific column
    private func getHeightForColumn(_ height: Double) -> Double {
        // If the height is already valid, return it
        if height > 1.0 {
            return height
        }

        // If selectedHeight is valid, use it as a fallback
        if selectedHeight > 1.0 {
            return selectedHeight
        }

        // Get all heights from the session
        let heights = session.getGridHeights()

        // If we have a valid selectedHeightIndex, use it directly
        // This is the most reliable way to get the correct height
        if let selectedHeightIndex = selectedHeightIndex, selectedHeightIndex >= 0 && selectedHeightIndex < heights.count {
            let correctHeight = heights[selectedHeightIndex]
            return correctHeight
        }

        // Try to find the column index
        if let columnIndex = heights.firstIndex(of: height) {
            // If we have a valid height at this index, use it
            if columnIndex < heights.count {
                let correctHeight = heights[columnIndex]
                return correctHeight
            }
        }

        // If we can't find the column or the height is invalid, use the first height
        if let firstHeight = heights.first {
            return firstHeight
        }

        // If there are no heights, use a default
        return 300.0 // Default to 300cm (about 9'10")
    }

    private func findNextEmptyCell(after height: Double, attemptIndex: Int16) -> (Double, Int16)? {
        let heights = session.getGridHeights()

        // Check if the current jump was a make
        let currentJump = findJump(height: height, attemptIndex: attemptIndex)
        let wasMake = currentJump?.result == "make"

        // If the jump was a make, move to the next height
        if wasMake {
            if let currentIndex = heights.firstIndex(of: height), currentIndex < heights.count - 1 {
                let nextHeight = heights[currentIndex + 1]

                // Try to find the first empty attempt at the next height
                for attempt in 1...3 {
                    if findJump(height: nextHeight, attemptIndex: Int16(attempt)) == nil {
                        return (nextHeight, Int16(attempt))
                    }
                }
            }
        } else {
            // For misses or passes, try to find the next attempt at the same height
            if attemptIndex < 3 {
                let nextAttempt = attemptIndex + 1
                if findJump(height: height, attemptIndex: nextAttempt) == nil {
                    return (height, nextAttempt)
                }
            }

            // If we're at the last attempt, move to the next height
            if let currentIndex = heights.firstIndex(of: height), currentIndex < heights.count - 1 {
                let nextHeight = heights[currentIndex + 1]

                // Try to find the first empty attempt at the next height
                for attempt in 1...3 {
                    if findJump(height: nextHeight, attemptIndex: Int16(attempt)) == nil {
                        return (nextHeight, Int16(attempt))
                    }
                }
            }
        }

        // If no empty cell is found, return nil
        return nil
    }
}

// MARK: - Supporting Views

struct GridCell: View {
    @Environment(\.managedObjectContext) private var viewContext

    let jump: Jump?
    let height: Double
    let attemptIndex: Int16
    let viewModel: SessionViewModel
    let onTap: () -> Void

    // Fetch all jumps for this session to check for pass logic
    @FetchRequest private var jumps: FetchedResults<Jump>

    init(jump: Jump?, height: Double, attemptIndex: Int16, viewModel: SessionViewModel, onTap: @escaping () -> Void) {
        self.jump = jump
        self.height = height
        self.attemptIndex = attemptIndex
        self.viewModel = viewModel
        self.onTap = onTap

        // Get the context from the shared PersistenceController
        let context = PersistenceController.shared.container.viewContext

        // Create a fetch request for jumps at this height using our entity extension
        let fetchRequest = Jump.fetchRequest(
            in: context,
            sortDescriptors: [
                NSSortDescriptor(keyPath: \Jump.attemptIndex, ascending: true)
            ]
        )

        if let session = jump?.session {
            fetchRequest.predicate = NSPredicate(format: "session == %@ AND barHeightCm == %f", session, height)
        } else {
            // Fallback predicate if jump is nil
            fetchRequest.predicate = NSPredicate(format: "barHeightCm == %f", height)
        }

        // Initialize the fetch request
        _jumps = FetchRequest(fetchRequest: fetchRequest)
    }

    var body: some View {
        ZStack {
            Rectangle()
                .fill(Color.gray.opacity(0.1))

            if let jump = jump {
                // Show result - clicking on it opens the edit form
                Button(action: onTap) {
                    ZStack {
                        Circle()
                            .fill(resultColor(for: jump))
                            .frame(width: 36, height: 36)

                        Text(resultSymbol(for: jump))
                            .font(.headline)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                    }
                }
                .buttonStyle(BorderlessButtonStyle())
                .disabled(false) // Always allow editing existing jumps

                // Show bar/bungee indicator for practice sessions
                if let session = jump.session, session.type?.lowercased() == "practice" {
                    VStack {
                        HStack {
                            Spacer()
                            Image(systemName: jump.useBar ? "minus" : "water.waves")
                                .font(.caption2)
                                .foregroundColor(jump.useBar ? .primary : .blue)
                                .padding(4)
                        }
                        Spacer()
                    }
                }

                // Show media indicator if jump has media (without playback functionality)
                if hasMedia(jump) {
                    VStack {
                        Spacer()
                        HStack {
                            Spacer()
                            Image(systemName: hasVideo(jump) ? "video.fill" : "photo.fill")
                                .font(.system(size: 14))
                                .foregroundColor(.blue)
                                .padding(4)
                        }
                    }
                }
            } else if isDisabledDueToPass || isDisabledDueToMake {
                // Show greyed out cell for pass or after make
                ZStack {
                    Circle()
                        .fill(Color.gray.opacity(0.3))
                        .frame(width: 36, height: 36)

                    Text("–")
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                }
            } else if viewModel.isOut && viewModel.shouldShowOut(height: height, attemptIndex: attemptIndex) {
                // Show OUT when athlete has 3 consecutive misses
                ZStack {
                    Circle()
                        .fill(Color.gray.opacity(0.5))
                        .frame(width: 36, height: 36)

                    Text("OUT")
                        .font(.caption)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                }
            } else {
                // Empty cell - clicking on it creates a new jump
                Button(action: onTap) {
                    Color.clear
                }
                .buttonStyle(BorderlessButtonStyle())
            }
        }
        .frame(width: 100, height: 60)
        .disabled(jump == nil && (isDisabledDueToPass || isDisabledDueToMake || viewModel.isCellDisabledDueToOut(height: height, attemptIndex: attemptIndex)))
        .overlay(
            Rectangle()
                .stroke(Color.gray.opacity(0.3), lineWidth: 0.5)
        )
    }

    // Check if this cell should be disabled due to pass logic
    private var isDisabledDueToPass: Bool {
        // Use the view model to check if this cell should be disabled due to pass
        return viewModel.isCellDisabledDueToPass(height: height, attemptIndex: attemptIndex)
    }

    // Check if this cell should be disabled due to a make at the same height
    private var isDisabledDueToMake: Bool {
        // Use the view model to check if this cell should be disabled due to a make
        return viewModel.isCellDisabledDueToMake(height: height, attemptIndex: attemptIndex)
    }

    private func resultColor(for jump: Jump) -> Color {
        if let result = jump.result {
            switch result {
            case "make":
                return .green
            case "miss":
                return .red
            case "pass":
                return .gray
            default:
                return .gray
            }
        }
        return .gray
    }

    private func resultSymbol(for jump: Jump) -> String {
        if let resultCode = jump.resultCode {
            return resultCode
        } else if let result = jump.result {
            return result == "make" ? "O" : result == "miss" ? "X" : "P"
        }
        return "X"
    }

    // Check if the jump has any media attached
    private func hasMedia(_ jump: Jump) -> Bool {
        // Check for media items
        let mediaItems = jump.getMediaItems()
        return !mediaItems.isEmpty
    }

    // Check if the jump has a video attached
    private func hasVideo(_ jump: Jump) -> Bool {
        // Check for video media items
        let mediaItems = jump.getMediaItems()
        return mediaItems.contains(where: { $0.type == .video })
    }
}

struct HeightEditSheet: View {
    @Environment(\.dismiss) private var dismiss

    // Use the global AppTheme.useMetricSystem directly
    private var useMetric: Bool { AppTheme.useMetricSystem }

    let height: Double
    let onDelete: () -> Void
    let onDuplicate: () -> Void
    let onEdit: () -> Void
    let onAddNew: () -> Void

    var body: some View {
        NavigationStack {
            List {
                Section {
                    HStack {
                        Text("Height")
                        Spacer()

                        // Display both units with preferred unit first and bold
                        if useMetric {
                            Text(HeightConverter.cmToMetersString(height))
                                .fontWeight(.bold)
                            Text("•")
                                .foregroundColor(.secondary)
                            Text(HeightConverter.cmToFeetInchesString(height))
                                .foregroundColor(.secondary)
                        } else {
                            Text(HeightConverter.cmToFeetInchesString(height))
                                .fontWeight(.bold)
                            Text("•")
                                .foregroundColor(.secondary)
                            Text(HeightConverter.cmToMetersString(height))
                                .foregroundColor(.secondary)
                        }
                    }
                }

                Section {
                    Button(action: onAddNew) {
                        Label("Add New Height", systemImage: "plus.circle")
                            .foregroundColor(AppTheme.accentColor)
                    }

                    Button(action: onEdit) {
                        Label("Edit Height", systemImage: "pencil")
                    }

                    Button(action: onDuplicate) {
                        Label("Duplicate Column", systemImage: "plus.rectangle.on.rectangle")
                    }

                    Button(role: .destructive, action: onDelete) {
                        Label("Delete Column", systemImage: "trash")
                    }
                }
            }
            .navigationTitle("Height Options")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - Height Picker Sheet Models
struct HeightPickerSheetItem: Identifiable {
    let id = UUID()
}

struct EditHeightSheet: View {
    @Environment(\.dismiss) private var dismiss

    // Use the global AppTheme.useMetricSystem directly
    private var useMetric: Bool { AppTheme.useMetricSystem }
    @AppStorage("barHeightMinCm") private var barHeightMinCm: Double = 25 // 0.25m in cm
    @AppStorage("barHeightMaxCm") private var barHeightMaxCm: Double = 700 // 7.00m in cm

    @State private var heightCm: Double
    @State private var heightPickerSheet: HeightPickerSheetItem?

    let originalHeight: Double
    let sessionType: String
    let heightStandard: String?
    let onUpdate: (Double) -> Void

    init(originalHeight: Double, sessionType: String = "practice", heightStandard: String? = "Free-Range", onUpdate: @escaping (Double) -> Void) {
        // Ensure we're not using 0 as the height
        let validHeight = originalHeight > 0 ? originalHeight : 300

        // Removed print statement to prevent log spam

        // Get the current metric setting
        let useMetricSetting = UserDefaults.standard.bool(forKey: "useMetricSystem")

        // If using imperial, round to the nearest quarter-inch
        let roundedHeight = !useMetricSetting ? HeightConverter.roundToQuarterInchInCm(validHeight) : validHeight

        self._heightCm = State(initialValue: roundedHeight)
        self.originalHeight = originalHeight
        self.sessionType = sessionType
        self.heightStandard = heightStandard
        self.onUpdate = onUpdate

        // Removed print statement to prevent log spam
    }

    var body: some View {
        NavigationStack {
            Form {
                Section {
                    Button(action: {
                        heightPickerSheet = HeightPickerSheetItem()
                    }) {
                        HStack {
                            Text("Height")
                            Spacer()

                            // Display both units with preferred unit first and bold
                            if useMetric {
                                Text(HeightConverter.cmToMetersString(heightCm))
                                    .fontWeight(.bold)
                                Text("•")
                                    .foregroundColor(.secondary)
                                Text(HeightConverter.cmToFeetInchesString(heightCm))
                                    .foregroundColor(.secondary)
                            } else {
                                Text(HeightConverter.cmToFeetInchesString(heightCm))
                                    .fontWeight(.bold)
                                Text("•")
                                    .foregroundColor(.secondary)
                                Text(HeightConverter.cmToMetersString(heightCm))
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                }
                // Removed onAppear handler with print statement to prevent log spam

                Section {
                    Button(action: {
                        onUpdate(heightCm)
                    }) {
                        Text("Update Height")
                            .frame(maxWidth: .infinity, alignment: .center)
                            .foregroundColor(AppTheme.accentColor)
                    }
                }
            }
            .navigationTitle("Edit Height")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
            .sheet(item: $heightPickerSheet) { _ in
                HeightSelectorView(
                    selectedHeight: $heightCm,
                    onSelect: { _ in },
                    sessionType: sessionType,
                    heightStandard: heightStandard
                )
            }
        }
    }
}

struct AddHeightSheet: View {
    @Environment(\.dismiss) private var dismiss

    // Use the global AppTheme.useMetricSystem directly
    private var useMetric: Bool { AppTheme.useMetricSystem }
    @AppStorage("barHeightMinCm") private var barHeightMinCm: Double = 25 // 0.25m in cm
    @AppStorage("barHeightMaxCm") private var barHeightMaxCm: Double = 700 // 7.00m in cm

    @State private var heightCm: Double
    @State private var heightPickerSheet: HeightPickerSheetItem?

    let sessionType: String
    let heightStandard: String?
    let onAdd: (Double) -> Void

    init(initialHeight: Double, sessionType: String = "practice", heightStandard: String? = "Free-Range", onAdd: @escaping (Double) -> Void) {
        // Ensure we have a valid initial height (at least 0.25m)
        let validHeight = max(initialHeight, 25) // 0.25m in cm

        // Get the current metric setting
        let useMetricSetting = UserDefaults.standard.bool(forKey: "useMetricSystem")

        // If using imperial, round to the nearest quarter-inch
        let roundedHeight = !useMetricSetting ? HeightConverter.roundToQuarterInchInCm(validHeight) : validHeight

        self._heightCm = State(initialValue: roundedHeight)
        self.sessionType = sessionType
        self.heightStandard = heightStandard
        self.onAdd = onAdd

        // Removed print statement to prevent log spam
    }

    var body: some View {
        NavigationStack {
            Form {
                Section {
                    Button(action: {
                        heightPickerSheet = HeightPickerSheetItem()
                    }) {
                        HStack {
                            Text("Height")
                            Spacer()

                            // Display both units with preferred unit first and bold
                            if useMetric {
                                Text(HeightConverter.cmToMetersString(heightCm))
                                    .fontWeight(.bold)
                                Text("•")
                                    .foregroundColor(.secondary)
                                Text(HeightConverter.cmToFeetInchesString(heightCm))
                                    .foregroundColor(.secondary)
                            } else {
                                Text(HeightConverter.cmToFeetInchesString(heightCm))
                                    .fontWeight(.bold)
                                Text("•")
                                    .foregroundColor(.secondary)
                                Text(HeightConverter.cmToMetersString(heightCm))
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                }

                Section {
                    Button(action: {
                        onAdd(heightCm)
                    }) {
                        Text("Add Height")
                            .frame(maxWidth: .infinity, alignment: .center)
                            .foregroundColor(AppTheme.accentColor)
                    }
                }
            }
            .navigationTitle("Add Height")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
            .sheet(item: $heightPickerSheet) { _ in
                HeightSelectorView(
                    selectedHeight: $heightCm,
                    onSelect: { _ in },
                    sessionType: sessionType,
                    heightStandard: heightStandard
                )
            }
        }
    }
}

// MARK: - Preview

struct JumpGridView_Previews: PreviewProvider {
    static var previews: some View {
        let context = PersistenceController.preview.container.viewContext
        let session = Session.create(in: context, athlete: Athlete.create(in: context, name: "Test Athlete"), type: "practice")

        return JumpGridView(session: session)
            .environment(\.managedObjectContext, context)
    }
}
