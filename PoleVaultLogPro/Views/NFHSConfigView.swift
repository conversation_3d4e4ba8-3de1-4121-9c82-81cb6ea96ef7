import SwiftUI

struct NFHSConfigView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var standardsManager = MeetStandardsManager.shared
    
    // NFHS configuration
    @State private var startingHeight: Double
    @State private var increment: Double
    @State private var maxHeight: Double
    
    // Increment options in cm
    private let incrementOptions: [(label: String, value: Double)] = [
        ("15 cm (6\")", 15),
        ("10 cm (4\")", 10),
        ("8 cm (3\")", 8)
    ]
    
    init() {
        // Initialize with current NFHS settings
        let nfhs = MeetStandardsManager.nfhsStandard
        _startingHeight = State(initialValue: nfhs.startingHeight)
        _increment = State(initialValue: nfhs.increment)
        _maxHeight = State(initialValue: nfhs.maxHeight)
    }
    
    var body: some View {
        NavigationStack {
            Form {
                Section(header: Text("NFHS Configuration")) {
                    Text("The NFHS standard uses a dynamic progression based on a starting height and fixed increment.")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .padding(.vertical, 4)
                    
                    HStack {
                        Text("Starting Height")
                        Spacer()
                        
                        if AppTheme.useMetricSystem {
                            Text("\(Int(startingHeight)) cm")
                                .fontWeight(.bold)
                            Text("•")
                            Text(HeightConverter.cmToFeetInchesString(startingHeight))
                                .foregroundColor(.secondary)
                        } else {
                            Text(HeightConverter.cmToFeetInchesString(startingHeight))
                                .fontWeight(.bold)
                            Text("•")
                            Text("\(Int(startingHeight)) cm")
                                .foregroundColor(.secondary)
                        }
                    }
                    
                    Slider(value: $startingHeight, in: 150...400, step: 1)
                    
                    Picker("Increment", selection: $increment) {
                        ForEach(incrementOptions, id: \.value) { option in
                            Text(option.label).tag(option.value)
                        }
                    }
                    .pickerStyle(SegmentedPickerStyle())
                    
                    HStack {
                        Text("Maximum Height")
                        Spacer()
                        
                        if AppTheme.useMetricSystem {
                            Text("\(Int(maxHeight)) cm")
                                .fontWeight(.bold)
                            Text("•")
                            Text(HeightConverter.cmToFeetInchesString(maxHeight))
                                .foregroundColor(.secondary)
                        } else {
                            Text(HeightConverter.cmToFeetInchesString(maxHeight))
                                .fontWeight(.bold)
                            Text("•")
                            Text("\(Int(maxHeight)) cm")
                                .foregroundColor(.secondary)
                        }
                    }
                    
                    Slider(value: $maxHeight, in: 400...700, step: 1)
                }
                
                Section(header: Text("Preview")) {
                    Text("This will generate heights from \(Int(startingHeight)) cm to \(Int(maxHeight)) cm in \(Int(increment)) cm increments.")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 12) {
                            ForEach(previewHeights, id: \.self) { height in
                                VStack {
                                    if AppTheme.useMetricSystem {
                                        Text("\(Int(height)) cm")
                                            .fontWeight(.bold)
                                        Text(HeightConverter.cmToFeetInchesString(height))
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                    } else {
                                        Text(HeightConverter.cmToFeetInchesString(height))
                                            .fontWeight(.bold)
                                        Text("\(Int(height)) cm")
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                    }
                                }
                                .padding(.vertical, 4)
                            }
                        }
                        .padding(.horizontal)
                    }
                    .frame(height: 60)
                }
                
                Section {
                    Button("Save Configuration") {
                        saveNFHSConfig()
                        dismiss()
                    }
                    .frame(maxWidth: .infinity, alignment: .center)
                    .foregroundColor(AppTheme.accentColor)
                }
            }
            .navigationTitle("NFHS Configuration")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    private var previewHeights: [Double] {
        var heights: [Double] = []
        var currentHeight = startingHeight
        
        // Show first 10 heights or until max height
        while currentHeight <= maxHeight && heights.count < 10 {
            heights.append(currentHeight)
            currentHeight += increment
        }
        
        return heights
    }
    
    private func saveNFHSConfig() {
        // Create a new NFHS standard with the updated configuration
        let updatedNFHS = MeetStandard(
            id: "NFHS",
            name: "NFHS",
            description: "National Federation of State High School Associations",
            isBuiltIn: true,
            heights: [],
            isDynamic: true,
            startingHeight: startingHeight,
            increment: increment,
            maxHeight: maxHeight
        )
        
        // Save the configuration to UserDefaults
        if let encoded = try? JSONEncoder().encode(updatedNFHS) {
            UserDefaults.standard.set(encoded, forKey: "nfhsStandardConfig")
        }
        
        // Update the shared instance
        // Note: In a real app, you would need to reload this when the app starts
        // This is just for the demo to see the changes immediately
        let shared = MeetStandardsManager.shared
        if let privateProperty = Mirror(reflecting: shared).children.first(where: { $0.label == "_nfhsStandard" }) {
            if var nfhsStandard = privateProperty.value as? MeetStandard {
                nfhsStandard.startingHeight = startingHeight
                nfhsStandard.increment = increment
                nfhsStandard.maxHeight = maxHeight
                // We can't directly modify the private property, but this demonstrates the concept
            }
        }
    }
}

#Preview {
    NFHSConfigView()
}
