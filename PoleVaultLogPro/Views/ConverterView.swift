import SwiftUI

struct ConverterView: View {
    @State private var searchText = ""
    @State private var scrollToHeight: Double?
    @State private var showingCopiedToast = false
    @State private var copiedHeight = ""
    @State private var hasAppeared = false

    // Use the global AppTheme.useMetricSystem directly
    private var useMetric: Bool { AppTheme.useMetricSystem }

    // Default height to show on load (approximately 10 feet)
    private let defaultHeight: Double = 305.0 // 10 feet in cm

    // Generate heights from 0.01m (1cm) to 7.00m (700cm) in 0.01m (1cm) increments
    private let heights: [Double] = stride(from: 1.0, through: 700.0, by: 1.0).map { $0 }

    var filteredHeights: [Double] {
        if searchText.isEmpty {
            return heights
        } else {
            // Try to parse the search text as a number
            if let searchValue = Double(searchText.replacingOccurrences(of: ",", with: ".")) {
                // Convert to cm if it looks like a meter value (less than 10)
                let searchCm = searchValue < 10 ? searchValue * 100 : searchValue

                // Find heights close to the search value
                return heights.filter { height in
                    // Check if the height in cm is close to the search value
                    let heightInMeters = height / 100

                    // Match by cm or meters
                    return abs(height - searchCm) < 5 ||
                           height.description.contains(searchText) ||
                           String(format: "%.2f", heightInMeters).contains(searchText)
                }
            } else {
                // Search in the formatted strings
                return heights.filter { height in
                    let metersString = HeightConverter.cmToMetersString(height)
                    let feetInchesString = HeightConverter.cmToFeetInchesString(height)
                    let feetDecimalInchesString = HeightConverter.cmToFeetDecimalInchesString(height)

                    return metersString.localizedCaseInsensitiveContains(searchText) ||
                           feetInchesString.localizedCaseInsensitiveContains(searchText) ||
                           feetDecimalInchesString.localizedCaseInsensitiveContains(searchText)
                }
            }
        }
    }

    var body: some View {
        NavigationStack {
            VStack(spacing: 0) {
                // Search bar
                searchBar

                // Height list
                ScrollViewReader { scrollProxy in
                    List {
                        // Header row
                        Section(header:
                            HStack {
                                Text("Meters")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                    .frame(minWidth: 70, alignment: .leading)

                                Spacer()

                                Text("Feet/Inches")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                    .frame(minWidth: 90, alignment: .center)

                                Spacer()

                                Text("Decimal Inches")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                    .frame(minWidth: 70, alignment: .trailing)
                            }
                            .padding(.bottom, 5)
                        ) {
                            ForEach(filteredHeights, id: \.self) { heightCm in
                            heightRow(for: heightCm)
                                .id(heightCm)
                                .contentShape(Rectangle())
                                .onTapGesture {
                                    // Copy all formats to clipboard when tapped
                                    let metricString = HeightConverter.cmToMetersString(heightCm)
                                    let feetInchesString = HeightConverter.cmToFeetInchesString(heightCm)
                                    let feetDecimalInchesString = HeightConverter.cmToFeetDecimalInchesString(heightCm)

                                    let heightString = "\(metricString) | \(feetInchesString) | \(feetDecimalInchesString)"
                                    UIPasteboard.general.string = heightString

                                    // Show toast notification
                                    copiedHeight = useMetric ? metricString : feetInchesString
                                    withAnimation {
                                        showingCopiedToast = true
                                    }

                                    // Hide toast after 2 seconds
                                    DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                                        withAnimation {
                                            showingCopiedToast = false
                                        }
                                    }

                                    // Provide haptic feedback
                                    let generator = UIImpactFeedbackGenerator(style: .light)
                                    generator.impactOccurred()
                                }
                            }
                        }
                    }
                    .listStyle(.plain)
                    .onChange(of: scrollToHeight) { _, newValue in
                        if let height = newValue {
                            withAnimation {
                                scrollProxy.scrollTo(height, anchor: .center)
                            }
                            scrollToHeight = nil
                        }
                    }
                    .onAppear {
                        // Scroll to default height (around 10 feet) when the view first appears
                        if !hasAppeared {
                            // Delay slightly to ensure the list is fully loaded
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                                withAnimation {
                                    scrollProxy.scrollTo(defaultHeight, anchor: .center)
                                }
                                hasAppeared = true
                            }
                        }
                    }
                }
            }
            .navigationTitle("Height Converter")
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        // Clear search and scroll to default height (around 10 feet)
                        searchText = ""
                        scrollToHeight = defaultHeight
                    }) {
                        Image(systemName: "arrow.counterclockwise")
                    }
                }
            }
            .overlay(
                // Toast notification
                ZStack {
                    if showingCopiedToast {
                        VStack {
                            Spacer()
                            HStack {
                                Image(systemName: "doc.on.clipboard")
                                Text("\(copiedHeight) copied to clipboard")
                            }
                            .padding()
                            .background(Color(.systemGray6))
                            .cornerRadius(8)
                            .shadow(radius: 2)
                            .padding(.bottom, 20)
                            .transition(.move(edge: .bottom).combined(with: .opacity))
                        }
                    }
                }
            )
        }
    }

    private var searchBar: some View {
        VStack(spacing: 0) {
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)
                    .padding(.leading, 8)

                TextField("Search (e.g., 4.00m, 13' 1 1/4\", 13' 3.5\")", text: $searchText)
                    .padding(10)
                    .submitLabel(.search)
                    .autocorrectionDisabled()
                    .textInputAutocapitalization(.never)

                if !searchText.isEmpty {
                    Button(action: {
                        searchText = ""
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.secondary)
                    }
                    .padding(.trailing, 8)
                }
            }
            .background(Color(.systemGray6))
            .cornerRadius(8)
            .padding(.horizontal)
            .padding(.vertical, 8)
            .onChange(of: searchText) { _, newValue in
                // If the search text is a valid height, scroll to it
                if let searchValue = Double(newValue.replacingOccurrences(of: ",", with: ".")) {
                    // Convert to cm if it looks like a meter value (less than 10)
                    let searchCm = searchValue < 10 ? searchValue * 100 : searchValue

                    // Find the closest height
                    if let closestHeight = heights.min(by: { abs($0 - searchCm) < abs($1 - searchCm) }) {
                        scrollToHeight = closestHeight
                    }
                } else if newValue.contains("'") {
                    // Try to parse as feet/inches
                    if let cm = HeightConverter.parseFeetInchesString(newValue) {
                        if let closestHeight = heights.min(by: { abs($0 - cm) < abs($1 - cm) }) {
                            scrollToHeight = closestHeight
                        }
                    }
                }
            }

            Divider()
        }
    }

    @ViewBuilder
    private func heightRow(for heightCm: Double) -> some View {
        HStack {
            // Metric value
            Text(HeightConverter.cmToMetersString(heightCm))
                .fontWeight(useMetric ? .bold : .regular)
                .foregroundColor(useMetric ? .primary : .secondary)
                .frame(minWidth: 70, alignment: .leading)

            Spacer()

            // Divider 1
            Rectangle()
                .fill(Color.gray.opacity(0.3))
                .frame(width: 1, height: 20)

            Spacer()

            // Imperial value (feet and inches with fractions)
            Text(HeightConverter.cmToFeetInchesString(heightCm))
                .fontWeight(useMetric ? .regular : .bold)
                .foregroundColor(useMetric ? .secondary : .primary)
                .frame(minWidth: 90, alignment: .center)

            Spacer()

            // Divider 2
            Rectangle()
                .fill(Color.gray.opacity(0.3))
                .frame(width: 1, height: 20)

            Spacer()

            // Imperial value (feet and decimal inches)
            Text(HeightConverter.cmToFeetDecimalInchesString(heightCm))
                .fontWeight(useMetric ? .regular : .bold)
                .foregroundColor(useMetric ? .secondary : .primary)
                .frame(minWidth: 70, alignment: .trailing)
        }
        .padding(.vertical, 6)
    }
}

#Preview {
    ConverterView()
}
