import SwiftUI

struct PrivacyPolicyView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    Group {
                        Text("Privacy Policy")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .padding(.bottom, 10)
                        
                        Text("Last Updated: \(formattedCurrentDate())")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        
                        Text("Introduction")
                            .font(.title2)
                            .fontWeight(.bold)
                        
                        Text("PoleVaultLogPro is committed to protecting your privacy. This Privacy Policy explains how your personal information is collected, used, and disclosed by PoleVaultLogPro.")
                            .fixedSize(horizontal: false, vertical: true)
                        
                        Text("Information We Collect")
                            .font(.title2)
                            .fontWeight(.bold)
                        
                        Text("PoleVaultLogPro collects and stores the following information locally on your device:")
                            .fixedSize(horizontal: false, vertical: true)
                        
                        bulletPoint("Athlete information (name, date of birth, dominant hand)")
                        bulletPoint("Session details (date, location, weather conditions)")
                        bulletPoint("Jump data (heights, results, technical measurements)")
                        bulletPoint("Equipment information (poles and their specifications)")
                        bulletPoint("Media links to photos and videos in your Photos library")
                    }
                    
                    Group {
                        Text("Data Storage and Sync")
                            .font(.title2)
                            .fontWeight(.bold)
                        
                        Text("All data is stored locally on your device using Apple's Core Data framework. If you enable iCloud sync, your data will be synchronized across your devices using Apple's CloudKit service. We do not have access to this data, as it is stored in your personal iCloud account.")
                            .fixedSize(horizontal: false, vertical: true)
                        
                        Text("Media Handling")
                            .font(.title2)
                            .fontWeight(.bold)
                        
                        Text("PoleVaultLogPro does not store copies of your photos or videos. Instead, it creates links to media in your Photos library. These links are stored in Core Data and synchronized via iCloud if enabled.")
                            .fixedSize(horizontal: false, vertical: true)
                        
                        Text("Third-Party Services")
                            .font(.title2)
                            .fontWeight(.bold)
                        
                        Text("PoleVaultLogPro does not share your data with any third-party services or analytics providers. We do not collect usage statistics or crash reports.")
                            .fixedSize(horizontal: false, vertical: true)
                    }
                    
                    Group {
                        Text("Your Rights")
                            .font(.title2)
                            .fontWeight(.bold)
                        
                        Text("You have complete control over your data. You can:")
                            .fixedSize(horizontal: false, vertical: true)
                        
                        bulletPoint("Export your data at any time")
                        bulletPoint("Delete all data using the Reset All Data function")
                        bulletPoint("Disable iCloud sync to keep data only on your device")
                        
                        Text("Changes to This Privacy Policy")
                            .font(.title2)
                            .fontWeight(.bold)
                        
                        Text("We may update our Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy in the app.")
                            .fixedSize(horizontal: false, vertical: true)
                        
                        Text("Contact Us")
                            .font(.title2)
                            .fontWeight(.bold)
                        
                        Text("If you have any questions about this Privacy Policy, please contact us at:")
                            .fixedSize(horizontal: false, vertical: true)
                        
                        Link("<EMAIL>", destination: URL(string: "mailto:<EMAIL>")!)
                            .foregroundColor(AppTheme.accentColor)
                    }
                }
                .padding()
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    private func bulletPoint(_ text: String) -> some View {
        HStack(alignment: .top) {
            Text("•")
                .padding(.trailing, 5)
            Text(text)
                .fixedSize(horizontal: false, vertical: true)
        }
        .padding(.leading)
    }
    
    private func formattedCurrentDate() -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateStyle = .long
        return dateFormatter.string(from: Date())
    }
}

#Preview {
    PrivacyPolicyView()
}
