import SwiftUI
import PhotosUI

struct HistoryView: View {
    @Environment(\.managedObjectContext) private var viewContext

    @FetchRequest private var sessions: FetchedResults<Session>

    @State private var searchText = ""
    @State private var showingFilters = false
    @State private var refreshID = UUID() // State to force refresh when media files change
    @State private var filterType: String? = nil
    @State private var isGridView = false

    // Listen for data reset and CloudKit sync notifications
    init() {
        // We need to use a different approach for struct views
        // The notification will be handled by the parent view controller refreshing the view

        // Initialize the fetch request using the helper
        _sessions = FetchRequest(fetchRequest: CoreDataFetchRequestHelper.createSessionFetchRequest(
            context: PersistenceController.shared.container.viewContext,
            sortDescriptors: [NSSortDescriptor(keyPath: \Session.date, ascending: false)]
        ))
    }

    // Setup notification observers when the view appears
    private func setupNotificationObservers() {
        // Listen for CloudKit data changes
        NotificationCenter.default.addObserver(
            forName: CloudKitSyncManager.NotificationName.cloudKitDataChanged,
            object: nil,
            queue: .main
        ) { [self] _ in
            print("HistoryView received CloudKit data change notification")
            refreshID = UUID()
        }
    }

    var body: some View {
        NavigationStack {
            ZStack {
                // Background watermark
                WatermarkView()

                if sessions.isEmpty {
                    // Empty state with ScrollView
                    ScrollView {
                        VStack(spacing: 20) {
                            PoleVaultIcon(size: CGSize(width: 80, height: 80), foregroundColor: AppTheme.accentColor)

                            Text("No Session History")
                                .font(.title2)
                                .fontWeight(.bold)

                            Text("Your past sessions will appear here")
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.center)
                                .padding(.horizontal)
                        }
                        .padding(.top, 40)
                        .padding(.horizontal)
                    }
                } else {
                    VStack(spacing: 0) {
                        // Filter and view toggle bar
                        HStack {
                            Button(action: {
                                showingFilters = true
                            }) {
                                HStack {
                                    Image(systemName: "line.3.horizontal.decrease.circle")
                                    Text(filterType ?? "All Sessions")
                                }
                                .padding(.vertical, 8)
                                .padding(.horizontal, 12)
                                .background(Color.gray.opacity(0.1))
                                .cornerRadius(8)
                            }

                            Spacer()

                            // View toggle
                            Button(action: {
                                withAnimation {
                                    isGridView.toggle()
                                }
                            }) {
                                Image(systemName: isGridView ? "list.bullet" : "square.grid.2x2")
                                    .padding(8)
                                    .background(Color.gray.opacity(0.1))
                                    .cornerRadius(8)
                            }
                            .buttonStyle(BorderlessButtonStyle())

                            Text("\(filteredSessions.count) sessions")
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .padding(.leading, 8)
                        }
                        .padding(.horizontal)
                        .padding(.vertical, 8)

                        if isGridView {
                            // Grid view
                            ScrollView {
                                LazyVGrid(columns: [GridItem(.adaptive(minimum: 160, maximum: 200))], spacing: 16) {
                                    ForEach(groupedSessions.keys.sorted(by: >), id: \.self) { month in
                                        Section(header: monthHeader(for: month)) {
                                            ForEach(groupedSessions[month] ?? []) { session in
                                                NavigationLink(destination: SessionDetailView(session: session)) {
                                                    SessionGridCard(session: session)
                                                }
                                                .buttonStyle(PlainButtonStyle())
                                            }
                                        }
                                    }
                                }
                                .padding()
                            }
                            .searchable(text: $searchText, prompt: "Search sessions")
                        } else {
                            // List view with collapsible sections
                            List {
                                ForEach(groupedSessions.keys.sorted(by: >), id: \.self) { month in
                                    Section(header: monthHeader(for: month)) {
                                        ForEach(groupedSessions[month] ?? []) { session in
                                            NavigationLink(destination: SessionDetailView(session: session)) {
                                                HistorySessionRow(session: session)
                                            }
                                        }
                                    }
                                }
                            }
                            .searchable(text: $searchText, prompt: "Search sessions")
                        }
                    }
                }
            }
            .navigationTitle("History")
            .confirmationDialog("Filter Sessions", isPresented: $showingFilters) {
                Button("All Sessions") { filterType = nil }
                Button("Practice Only") { filterType = "Practice" }
                Button("Meets Only") { filterType = "Meet" }
                Button("Cancel", role: .cancel) { }
            }
            .id(refreshID) // Force refresh when refreshID changes
            .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("MediaFilesChanged"))) { _ in
                // Refresh the view when media files are changed
                print("Refreshing HistoryView due to media files change")
                refreshID = UUID() // Force a refresh
            }
            .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("RefreshFetchRequests"))) { _ in
                // Refresh the view when fetch requests need to be refreshed
                print("Refreshing HistoryView due to fetch request refresh")
                refreshID = UUID() // Force a refresh
            }
            .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("CoreDataContextReset"))) { _ in
                // Refresh the view when the Core Data context is reset
                print("Refreshing HistoryView due to Core Data context reset")

                // Force a refresh of the view
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    refreshID = UUID()
                }
            }
            .onAppear {
                setupNotificationObservers()
            }
        }
    }

    // Helper to create month section headers
    @ViewBuilder
    private func monthHeader(for month: String) -> some View {
        HStack {
            Text(month)
                .font(.headline)
                .foregroundColor(.primary)

            Spacer()

            if let bestHeight = monthBestHeights[month], bestHeight > 0 {
                if AppTheme.useMetricSystem {
                    Text(HeightConverter.cmToMetersString(bestHeight))
                        .font(.caption)
                        .foregroundColor(.white)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(AppTheme.accentColor)
                        .cornerRadius(12)
                } else {
                    Text(HeightConverter.cmToFeetInchesString(bestHeight))
                        .font(.caption)
                        .foregroundColor(.white)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(AppTheme.accentColor)
                        .cornerRadius(12)
                }
            }
        }
        .padding(.vertical, 8)
    }

    // Filter sessions based on search and type
    private var filteredSessions: [Session] {
        let filtered = sessions.filter { session in
            // Apply search filter
            let searchMatches = searchText.isEmpty ||
                (session.title?.localizedCaseInsensitiveContains(searchText) ?? false) ||
                (session.location?.localizedCaseInsensitiveContains(searchText) ?? false)

            // Apply type filter
            let typeMatches = filterType == nil || session.type == filterType

            return searchMatches && typeMatches
        }

        return filtered
    }

    // Group sessions by month
    private var groupedSessions: [String: [Session]] {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "MMMM yyyy"

        var result: [String: [Session]] = [:]

        for session in filteredSessions {
            // Convert TimeInterval to Date
            let date = Date(timeIntervalSinceReferenceDate: session.date)
            let monthYearString = dateFormatter.string(from: date)
            if result[monthYearString] == nil {
                result[monthYearString] = []
            }
            result[monthYearString]?.append(session)
        }

        return result
    }

    // Calculate best height for each month
    private var monthBestHeights: [String: Double] {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "MMMM yyyy"

        var result: [String: Double] = [:]

        for session in filteredSessions {
            // Convert TimeInterval to Date
            let date = Date(timeIntervalSinceReferenceDate: session.date)
            let monthYearString = dateFormatter.string(from: date)
            let currentBest = result[monthYearString] ?? 0

            if session.bestHeightCm > currentBest {
                result[monthYearString] = session.bestHeightCm
            }
        }

        return result
    }
}

struct HistorySessionRow: View {
    @ObservedObject var session: Session
    @State private var refreshID = UUID() // State to force refresh when media files change

    @FetchRequest private var jumps: FetchedResults<Jump>

    init(session: Session) {
        self.session = session

        // Configure the fetch request for jumps using the helper
        _jumps = FetchRequest(fetchRequest: CoreDataFetchRequestHelper.createJumpFetchRequest(
            context: PersistenceController.shared.container.viewContext,
            sortDescriptors: [NSSortDescriptor(keyPath: \Jump.order, ascending: true)],
            predicate: NSPredicate(format: "session == %@", session)
        ))
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                Text(session.title ?? "Untitled Session")
                    .font(.headline)

                Spacer()

                if session.bestHeightCm > 0 {
                    HStack(spacing: 4) {
                        // Primary unit (based on user preference)
                        Text(bestHeightString)
                            .font(.subheadline)
                            .fontWeight(.medium)

                        // Secondary unit
                        Text("•")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        if AppTheme.useMetricSystem {
                            Text(HeightConverter.cmToFeetInchesString(session.bestHeightCm))
                                .font(.caption)
                                .foregroundColor(.secondary)
                        } else {
                            Text(HeightConverter.cmToMetersString(session.bestHeightCm))
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                    .padding(.horizontal, 8)
                    .padding(.vertical, 2)
                    .background(AppTheme.accentColorLight.opacity(0.2))
                    .cornerRadius(8)
                }
            }

            HStack {
                VStack(alignment: .leading, spacing: 2) {
                    HStack(spacing: 6) {
                        // Date
                        // Convert TimeInterval to Date
                        let date = Date(timeIntervalSinceReferenceDate: session.date)
                        Text(DateFormatters.mediumDate.string(from: date))
                            .font(.subheadline)
                            .foregroundColor(.secondary)

                        // Type badge
                        if let type = session.type {
                            Text(type)
                                .font(.caption)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(type == "Practice" ? Color.gray.opacity(0.2) : Color.orange.opacity(0.2))
                                .cornerRadius(4)
                        }
                    }

                    // Location if available
                    if let location = session.location, !location.isEmpty {
                        Text(location)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }

                Spacer()

                // Jump statistics and media indicator
                if jumps.count > 0 {
                    HStack(spacing: 8) {
                        // Media indicator
                        if hasMedia {
                            Image(systemName: hasVideo ? "video.fill" : "photo.fill")
                                .foregroundColor(.blue)
                                .font(.caption)
                        }

                        HStack(spacing: 4) {
                            Label("\(jumps.filter { $0.result == "make" }.count)", systemImage: "checkmark.circle.fill")
                                .foregroundColor(.green)
                                .font(.caption)
                                .labelStyle(CompactLabelStyle())

                            Label("\(jumps.filter { $0.result == "miss" }.count)", systemImage: "xmark.circle.fill")
                                .foregroundColor(.red)
                                .font(.caption)
                                .labelStyle(CompactLabelStyle())

                            Label("\(jumps.filter { $0.result == "pass" }.count)", systemImage: "arrow.right.circle.fill")
                                .foregroundColor(.gray)
                                .font(.caption)
                                .labelStyle(CompactLabelStyle())
                        }
                    }
                }
            }
        }
        .padding(.vertical, 4)
        .id(refreshID) // Force refresh when refreshID changes
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("MediaFilesChanged"))) { _ in
            // Refresh the view when media files are changed
            print("Refreshing HistorySessionRow due to media files change")
            refreshID = UUID() // Force a refresh
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("RefreshFetchRequests"))) { _ in
            // Refresh the view when fetch requests need to be refreshed
            print("Refreshing HistorySessionRow due to fetch request refresh")
            refreshID = UUID() // Force a refresh
        }
    }

    private var bestHeightString: String {
        if AppTheme.useMetricSystem {
            return HeightConverter.cmToMetersString(session.bestHeightCm)
        } else {
            return HeightConverter.cmToFeetInchesString(session.bestHeightCm)
        }
    }

    // Check if any jump in the session has media attached
    private var hasMedia: Bool {
        for jump in jumps {
            // Check for media items
            let mediaItems = jump.getMediaItems()
            if !mediaItems.isEmpty {
                return true
            }
        }

        return false
    }

    // Check if any jump in the session has a video attached
    private var hasVideo: Bool {
        for jump in jumps {
            // Check for video media items
            let mediaItems = jump.getMediaItems()
            for media in mediaItems {
                if media.type == .video {
                    return true
                }
            }
        }

        return false
    }
}

struct SessionGridCard: View {
    @ObservedObject var session: Session
    @State private var thumbnailImage: UIImage? = nil
    @State private var refreshID = UUID() // State to force refresh when media files change

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // Thumbnail or placeholder
            ZStack {
                if let thumbnail = thumbnailImage {
                    Image(uiImage: thumbnail)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(height: 120)
                        .clipped()
                } else {
                    Rectangle()
                        .fill(Color.gray.opacity(0.2))
                        .frame(height: 120)

                    Image(systemName: "photo")
                        .font(.system(size: 30))
                        .foregroundColor(.gray)
                }

                // Session type badge and media indicator
                VStack {
                    HStack {
                        // Media indicator
                        if hasMedia {
                            Image(systemName: hasVideo ? "video.fill" : "photo.fill")
                                .font(.caption2)
                                .foregroundColor(.blue)
                                .padding(4)
                                .background(Color.white.opacity(0.8))
                                .cornerRadius(4)
                        }

                        Spacer()

                        // Session type badge
                        if let type = session.type {
                            Text(type)
                                .font(.caption2)
                                .fontWeight(.medium)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 3)
                                .background(type == "Practice" ? Color.gray.opacity(0.8) : Color.orange.opacity(0.8))
                                .foregroundColor(.white)
                                .cornerRadius(4)
                        }
                    }

                    Spacer()
                }
                .padding(6)

                // Best height badge
                if session.bestHeightCm > 0 {
                    VStack {
                        Spacer()

                        HStack {
                            if AppTheme.useMetricSystem {
                                Text(HeightConverter.cmToMetersString(session.bestHeightCm))
                                    .font(.caption2)
                                    .fontWeight(.bold)
                                    .padding(.horizontal, 6)
                                    .padding(.vertical, 3)
                                    .background(AppTheme.accentColor)
                                    .foregroundColor(.white)
                                    .cornerRadius(4)
                            } else {
                                Text(HeightConverter.cmToFeetInchesString(session.bestHeightCm))
                                    .font(.caption2)
                                    .fontWeight(.bold)
                                    .padding(.horizontal, 6)
                                    .padding(.vertical, 3)
                                    .background(AppTheme.accentColor)
                                    .foregroundColor(.white)
                                    .cornerRadius(4)
                            }

                            Spacer()
                        }
                    }
                    .padding(6)
                }
            }

            // Session title
            Text(session.title ?? "Untitled Session")
                .font(.subheadline)
                .fontWeight(.medium)
                .lineLimit(1)

            // Date and location
            // Convert TimeInterval to Date
            let date = Date(timeIntervalSinceReferenceDate: session.date)
            Text(DateFormatters.mediumDate.string(from: date))
                .font(.caption)
                .foregroundColor(.secondary)

            if let location = session.location, !location.isEmpty {
                Text(location)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(1)
            }
        }
        .frame(maxWidth: .infinity)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
        .id(refreshID) // Force refresh when refreshID changes
        .onAppear {
            loadThumbnail()
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("MediaFilesChanged"))) { _ in
            // Refresh the view when media files are changed
            print("Refreshing SessionGridCard due to media files change")
            refreshID = UUID() // Force a refresh
            // Also reload the thumbnail
            loadThumbnail()
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("RefreshFetchRequests"))) { _ in
            // Refresh the view when fetch requests need to be refreshed
            print("Refreshing SessionGridCard due to fetch request refresh")
            refreshID = UUID() // Force a refresh
            // Also reload the thumbnail
            loadThumbnail()
        }
    }

    // Check if any jump in the session has media attached
    private var hasMedia: Bool {
        // Check jumps for media
        if let jumps = session.jumps?.allObjects as? [Jump] {
            for jump in jumps {
                // Check for media items
                let mediaItems = jump.getMediaItems()
                if !mediaItems.isEmpty {
                    return true
                }
            }
        }

        return false
    }

    // Check if any jump in the session has a video attached
    private var hasVideo: Bool {
        // Check jumps for videos
        if let jumps = session.jumps?.allObjects as? [Jump] {
            for jump in jumps {
                // Check for video media items
                let mediaItems = jump.getMediaItems()
                for media in mediaItems {
                    if media.type == .video {
                        return true
                    }
                }
            }
        }

        return false
    }

    private func loadThumbnail() {
        // Get the first jump with a video or photo
        guard let jumps = session.jumps?.allObjects as? [Jump] else {
            return
        }

        // Find the first jump with media
        for jump in jumps {
            let mediaItems = jump.getMediaItems()

            // Skip if no media items
            if mediaItems.isEmpty {
                continue
            }

            // Find the first media item with an asset identifier
            for media in mediaItems {
                guard let assetIdentifier = media.assetIdentifier else {
                    continue
                }

                // Load the thumbnail from the PHAsset
                // First try to find the asset as a shared asset
                var asset = SharedMediaManager.shared.fetchSharedAsset(with: assetIdentifier)

                // If not found as a shared asset, try as a local asset
                if asset == nil {
                    let fetchResult = PHAsset.fetchAssets(withLocalIdentifiers: [assetIdentifier], options: nil)
                    asset = fetchResult.firstObject
                }

                if let asset = asset {
                    let imageManager = PHImageManager.default()
                    let requestOptions = PHImageRequestOptions()
                    requestOptions.isSynchronous = false
                    requestOptions.deliveryMode = .opportunistic

                    imageManager.requestImage(
                        for: asset,
                        targetSize: CGSize(width: 300, height: 300),
                        contentMode: .aspectFill,
                        options: requestOptions
                    ) { image, _ in
                        if let image = image {
                            DispatchQueue.main.async {
                                self.thumbnailImage = image
                                return
                            }
                        }
                    }

                    // Return after requesting the first valid image
                    return
                }
            }
        }
    }
}

#Preview {
    let previewController = PersistenceController(inMemory: true)
    let context = previewController.container.viewContext

    do {
        // Create a sample session with jumps for preview
        let session = Session.create(in: context, athlete: Athlete.create(in: context, name: "Test Athlete"), type: "practice")
        session.title = "Practice Session"
        session.location = "School Track"
        session.date = Date().timeIntervalSinceReferenceDate

        // Add some jumps
        _ = Jump.create(in: context, session: session, order: 1, heightCm: 400, result: "make")
        _ = Jump.create(in: context, session: session, order: 2, heightCm: 420, result: "miss")
        _ = Jump.create(in: context, session: session, order: 3, heightCm: 420, result: "make")
        _ = Jump.create(in: context, session: session, order: 4, heightCm: 440, result: "pass")
        _ = Jump.create(in: context, session: session, order: 5, heightCm: 440, result: "miss")

        // Update best height
        session.bestHeightCm = 420
        session.bestHeightIn = 420 / 2.54

        try context.save()
    } catch {
        print("Error creating preview data: \(error)")
    }

    return HistoryView()
        .environment(\.managedObjectContext, context)
}
