import SwiftUI
import UniformTypeIdentifiers

struct ExportSessionView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.managedObjectContext) private var viewContext

    let session: Session

    @State private var isExporting = false
    @State private var exportURL: URL?
    @State private var exportType: ExportType = .json
    @State private var showingAlert = false
    @State private var alertTitle = ""
    @State private var alertMessage = ""

    enum ExportType: String, CaseIterable, Identifiable {
        case json = "JSON (.pvl.json)"
        case csv = "CSV (.csv)"

        var id: String { self.rawValue }

        var fileExtension: String {
            switch self {
            case .json:
                return ExportImportManager.jsonExtension
            case .csv:
                return ExportImportManager.csvExtension
            }
        }

        var utType: UTType {
            switch self {
            case .json:
                return ExportImportManager.jsonUTType
            case .csv:
                return ExportImportManager.csvUTType
            }
        }
    }

    var body: some View {
        NavigationStack {
            Form {
                Section(header: Text("Session Details")) {
                    HStack {
                        Text("Title")
                        Spacer()
                        Text(session.title ?? "Unknown")
                            .foregroundColor(.secondary)
                    }

                    HStack {
                        Text("Date")
                        Spacer()
                        Text(formattedDate)
                            .foregroundColor(.secondary)
                    }

                    HStack {
                        Text("Type")
                        Spacer()
                        Text(session.type ?? "Unknown")
                            .foregroundColor(.secondary)
                    }

                    HStack {
                        Text("Jumps")
                        Spacer()
                        Text("\(session.jumps?.count ?? 0)")
                            .foregroundColor(.secondary)
                    }
                }

                Section(header: Text("Export Format")) {
                    Picker("Format", selection: $exportType) {
                        ForEach(ExportType.allCases) { type in
                            Text(type.rawValue).tag(type)
                        }
                    }
                    .pickerStyle(SegmentedPickerStyle())
                }

                Section {
                    Button(action: exportSession) {
                        HStack {
                            Spacer()
                            if isExporting {
                                ProgressView()
                                    .padding(.trailing, 5)
                            }
                            Text("Export Session")
                            Spacer()
                        }
                    }
                    .disabled(isExporting)
                }
            }
            .navigationTitle("Export Session")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
            .fileExporter(
                isPresented: $isExporting,
                document: SessionExportDocument(url: exportURL),
                contentType: exportType.utType,
                defaultFilename: "Session-\(session.title ?? "Export")-\(formattedDate).\(exportType.fileExtension)"
            ) { result in
                isExporting = false

                switch result {
                case .success(let url):
                    alertTitle = "Success"
                    alertMessage = "Session exported successfully to \(url.lastPathComponent)"
                    showingAlert = true
                case .failure(let error):
                    alertTitle = "Error"
                    alertMessage = "Failed to export session: \(error.localizedDescription)"
                    showingAlert = true
                }
            }
            .alert(alertTitle, isPresented: $showingAlert) {
                Button("OK") {
                    if alertTitle == "Success" {
                        dismiss()
                    }
                }
            } message: {
                Text(alertMessage)
            }
        }
    }

    private var formattedDate: String {
        let date = Date(timeIntervalSinceReferenceDate: session.date)
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        return formatter.string(from: date)
    }

    private func exportSession() {
        isExporting = true

        do {
            switch exportType {
            case .json:
                exportURL = try ExportImportManager.shared.exportSessionToJSON(session)
            case .csv:
                exportURL = try ExportImportManager.shared.exportSessionToCSV(session)
            }
        } catch {
            isExporting = false
            alertTitle = "Error"
            alertMessage = "Failed to prepare export: \(error.localizedDescription)"
            showingAlert = true
        }
    }
}

/// A FileDocument for exporting session data
struct SessionExportDocument: FileDocument {
    static var readableContentTypes: [UTType] { [ExportImportManager.jsonUTType, ExportImportManager.csvUTType] }

    let url: URL?

    init(url: URL?) {
        self.url = url
    }

    init(configuration: ReadConfiguration) throws {
        url = nil
    }

    func fileWrapper(configuration: WriteConfiguration) throws -> FileWrapper {
        guard let url = url else {
            throw NSError(domain: "ExportSessionView", code: 1, userInfo: [NSLocalizedDescriptionKey: "No export URL provided"])
        }

        return try FileWrapper(url: url)
    }
}
