import SwiftUI
import Charts

struct JumpResultsScatterChart: View {
    struct JumpDataPoint: Identifiable {
        let id = UUID()
        let date: Date
        let height: Double
        let result: String
        let sessionTitle: String
        let comment: String?
        
        var resultColor: Color {
            switch result {
            case "make":
                return .green
            case "miss":
                return .red
            case "pass":
                return .gray
            default:
                return .gray
            }
        }
    }
    
    let data: [JumpDataPoint]
    @State private var selectedDataPoint: JumpDataPoint?
    
    var body: some View {
        Chart {
            ForEach(data) { dataPoint in
                PointMark(
                    x: .value("Date", dataPoint.date),
                    y: .value("Height", dataPoint.height)
                )
                .foregroundStyle(dataPoint.resultColor)
                .symbolSize(selectedDataPoint?.id == dataPoint.id ? 150 : 100)
            }
            
            if let selected = selectedDataPoint {
                RuleMark(
                    x: .value("Selected Date", selected.date)
                )
                .foregroundStyle(Color.gray.opacity(0.3))
                .lineStyle(StrokeStyle(lineWidth: 1, dash: [5, 5]))
                
                RuleMark(
                    y: .value("Selected Height", selected.height)
                )
                .foregroundStyle(Color.gray.opacity(0.3))
                .lineStyle(StrokeStyle(lineWidth: 1, dash: [5, 5]))
                
                PointMark(
                    x: .value("Date", selected.date),
                    y: .value("Height", selected.height)
                )
                .foregroundStyle(selected.resultColor)
                .symbolSize(150)
                .annotation(position: .top) {
                    VStack(alignment: .leading, spacing: 6) {
                        Text(selected.sessionTitle)
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        HStack {
                            if AppTheme.useMetricSystem {
                                Text(HeightConverter.cmToMetersString(selected.height))
                                    .font(.headline)
                                    .foregroundColor(AppTheme.accentColor)
                            } else {
                                Text(HeightConverter.cmToFeetInchesString(selected.height))
                                    .font(.headline)
                                    .foregroundColor(AppTheme.accentColor)
                            }
                            
                            Text(selected.result.capitalized)
                                .font(.caption)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(selected.resultColor.opacity(0.2))
                                .foregroundColor(selected.resultColor)
                                .cornerRadius(4)
                        }
                        
                        if let comment = selected.comment, !comment.isEmpty {
                            Text(comment)
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .lineLimit(2)
                        }
                    }
                    .padding(8)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color(.systemBackground))
                            .shadow(color: Color.black.opacity(0.1), radius: 3)
                    )
                }
            }
        }
        .chartXAxis {
            AxisMarks(values: .automatic) { value in
                AxisGridLine()
                AxisValueLabel {
                    if let date = value.as(Date.self) {
                        Text(DateFormatters.shortDate.string(from: date))
                            .font(.caption)
                    }
                }
            }
        }
        .chartYAxis {
            AxisMarks(position: .leading) { value in
                AxisGridLine()
                AxisValueLabel {
                    if let height = value.as(Double.self) {
                        if AppTheme.useMetricSystem {
                            Text(HeightConverter.cmToMetersString(height))
                                .font(.caption)
                        } else {
                            Text(HeightConverter.cmToFeetInchesString(height))
                                .font(.caption)
                        }
                    }
                }
            }
        }
        .chartOverlay { proxy in
            GeometryReader { geometry in
                Rectangle()
                    .fill(Color.clear)
                    .contentShape(Rectangle())
                    .gesture(
                        DragGesture()
                            .onChanged { value in
                                guard let plotFrame = proxy.plotFrame else {
                                    selectedDataPoint = nil
                                    return
                                }
                                let x = value.location.x - geometry[plotFrame].origin.x
                                let y = value.location.y - geometry[plotFrame].origin.y
                                guard x >= 0, x <= geometry[plotFrame].width,
                                      y >= 0, y <= geometry[plotFrame].height else {
                                    selectedDataPoint = nil
                                    return
                                }
                                
                                // Find the closest data point
                                let closestPoint = data.min(by: {
                                    let position1X = proxy.position(forX: $0.date) ?? 0
                                    let position1Y = proxy.position(forY: $0.height) ?? 0
                                    let position2X = proxy.position(forX: $1.date) ?? 0
                                    let position2Y = proxy.position(forY: $1.height) ?? 0
                                    
                                    let distance1 = sqrt(pow(position1X - x, 2) + pow(position1Y - y, 2))
                                    let distance2 = sqrt(pow(position2X - x, 2) + pow(position2Y - y, 2))
                                    
                                    return distance1 < distance2
                                })
                                selectedDataPoint = closestPoint
                            }
                            .onEnded { _ in
                                // Keep the selected point visible
                            }
                    )
            }
        }
        .frame(height: 220)
        .padding(.vertical)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
    }
}
