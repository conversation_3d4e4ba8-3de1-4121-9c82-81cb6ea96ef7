import SwiftUI

/// A reusable technical detail picker component that displays a picker directly in the form
/// with both metric and imperial units displayed according to user preference
struct InlineTechnicalDetailPicker: View {
    // Use the global AppTheme.useMetricSystem directly
    private var useMetric: Bool { AppTheme.useMetricSystem }

    // Binding to the selected value in cm
    @Binding var selectedValue: Double

    // Configuration for the picker
    let title: String
    let minCm: Double
    let maxCm: Double
    let step: Double
    let formatImperial: (Double) -> String
    let description: String

    // State for showing/hiding the picker
    @State private var showPicker = false

    // Generate the list of values based on the range and step
    private var valueOptions: [Double] {
        var values: [Double] = []
        let minValue = max(minCm, 0)
        let maxValue = min(maxCm, 10000) // Set a reasonable upper limit

        // If using imperial, use appropriate step size
        let stepSize: Double
        if useMetric {
            stepSize = step
        } else {
            // For hand hold and step measurements, use whole-inch steps
            if title == "Hand Hold" || title == "Run Start" || title == "Take-Off Step" {
                stepSize = HeightConverter.inchesToCm(1.0) // Whole inch
            } else {
                stepSize = HeightConverter.inchesToCm(0.25) // Quarter inch for other measurements
            }
        }

        // For hand hold and step measurements in imperial, ensure we generate values
        // that will display as proper feet/inches without duplicates
        if !useMetric && (title == "Hand Hold" || title == "Run Start" || title == "Take-Off Step") {
            // Generate values based on whole inches
            let minInches = Int(minValue / 2.54)
            let maxInches = Int(maxValue / 2.54)

            for totalInches in minInches...maxInches {
                let cm = Double(totalInches) * 2.54
                values.append(cm)
            }
        } else {
            // Standard approach for other measurements
            var currentValue = minValue
            while currentValue <= maxValue {
                values.append(currentValue)
                currentValue += stepSize
            }
        }

        // Make sure the current selected value is in the list
        if !values.contains(where: { abs($0 - selectedValue) < 0.01 }) {
            // Round the selected value to the nearest appropriate unit
            let roundedValue: Double
            if !useMetric && (title == "Hand Hold" || title == "Run Start" || title == "Take-Off Step") {
                roundedValue = HeightConverter.roundToWholeInchInCm(selectedValue)
            } else if !useMetric {
                roundedValue = HeightConverter.roundToQuarterInchInCm(selectedValue)
            } else {
                roundedValue = round(selectedValue / step) * step
            }

            // Only add if it's not already in the list
            if !values.contains(where: { abs($0 - roundedValue) < 0.01 }) {
                values.append(roundedValue)
                values.sort()
            }
        }

        return values
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            // Header row that toggles the picker
            HStack {
                Text(title)
                Spacer()
                if useMetric {
                    Text(HeightConverter.cmToMetersString(selectedValue))
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                } else {
                    Text(formatImperial(selectedValue))
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                }
                Image(systemName: "chevron.down")
                    .foregroundColor(.secondary)
                    .font(.caption)
                    .rotationEffect(Angle(degrees: showPicker ? 180 : 0))
                    .animation(.easeInOut, value: showPicker)
            }
            .contentShape(Rectangle())
            .onTapGesture {
                withAnimation {
                    showPicker.toggle()
                }
            }

            // Description text
            Text(description)
                .font(.caption)
                .foregroundColor(.secondary)
                .padding(.leading, 4)

            // Picker (shown only when expanded)
            if showPicker {
                Picker(title, selection: $selectedValue) {
                    ForEach(valueOptions, id: \.self) { value in
                        HStack {
                            if useMetric {
                                Text(HeightConverter.cmToMetersString(value))
                                    .tag(value)
                                Text("•")
                                Text(formatImperial(value))
                                    .foregroundColor(.secondary)
                            } else {
                                Text(formatImperial(value))
                                    .tag(value)
                                Text("•")
                                Text(HeightConverter.cmToMetersString(value))
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                }
                .pickerStyle(.wheel)
                .frame(height: 150)
                .padding(.vertical, 8)
                .transition(.opacity)
            }
        }
        .padding(.vertical, 8)
    }
}

// Helper formatters for different technical details
extension InlineTechnicalDetailPicker {
    // Format for Run Start and Take-Off Step (feet and whole inches)
    static func formatFeetInches(_ cm: Double) -> String {
        return HeightConverter.formatStepMeasurement(cm: cm)
    }

    // Format for Hand Hold (feet and whole inches)
    static func formatHandHold(_ cm: Double) -> String {
        return HeightConverter.formatHandHoldMeasurement(cm: cm)
    }

    // Format for Standard (inches only) - use HeightConverter function
    static func formatStandard(_ cm: Double) -> String {
        return HeightConverter.formatStandardMeasurement(cm: cm)
    }
}

#Preview {
    Form {
        InlineTechnicalDetailPicker(
            selectedValue: .constant(300),
            title: "Run Start",
            minCm: 0,
            maxCm: 3048,
            step: 5,
            formatImperial: InlineTechnicalDetailPicker.formatFeetInches,
            description: "Distance from starting point to takeoff"
        )
    }
}
