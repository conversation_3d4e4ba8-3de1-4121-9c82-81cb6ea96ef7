import SwiftUI
import AVKit
import PhotosUI
import Photos
import CoreData

/// A view that displays a photo using native iOS components
struct NativePhotoView: View {
    let media: JumpMedia
    @State private var image: UIImage?
    @State private var isLoading = true
    @State private var errorMessage: String?
    @State private var scale: CGFloat = 1.0
    @State private var lastScale: CGFloat = 1.0
    @State private var offset: CGSize = .zero
    @State private var lastOffset: CGSize = .zero

    var body: some View {
        ZStack {
            if isLoading {
                ProgressView()
                    .scaleEffect(1.5)
                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
            } else if let errorMessage = errorMessage {
                VStack(spacing: 16) {
                    Image(systemName: "exclamationmark.triangle")
                        .font(.system(size: 50))
                        .foregroundColor(.white)

                    Text("Error Loading Photo")
                        .font(.headline)
                        .foregroundColor(.white)

                    Text(errorMessage)
                        .font(.subheadline)
                        .foregroundColor(.gray)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)

                    Button("Retry") {
                        loadPhoto()
                    }
                    .padding()
                    .background(AppTheme.accentColor)
                    .foregroundColor(.white)
                    .cornerRadius(8)
                }
                .padding()
            } else if let image = image {
                Image(uiImage: image)
                    .resizable()
                    .scaledToFit()
                    .scaleEffect(scale)
                    .offset(offset)
                    .gesture(
                        MagnificationGesture()
                            .onChanged { value in
                                let delta = value / lastScale
                                lastScale = value

                                // Limit the minimum scale to 0.5 and maximum to 4
                                scale = min(max(scale * delta, 0.5), 4.0)
                            }
                            .onEnded { _ in
                                lastScale = 1.0
                            }
                    )
                    .gesture(
                        DragGesture()
                            .onChanged { value in
                                offset = CGSize(
                                    width: lastOffset.width + value.translation.width,
                                    height: lastOffset.height + value.translation.height
                                )
                            }
                            .onEnded { _ in
                                lastOffset = offset
                            }
                    )
                    .gesture(
                        TapGesture(count: 2)
                            .onEnded {
                                if scale > 1.0 {
                                    // Reset zoom and position if zoomed in
                                    withAnimation {
                                        scale = 1.0
                                        offset = .zero
                                        lastOffset = .zero
                                    }
                                } else {
                                    // Zoom to 2x if not zoomed in
                                    withAnimation {
                                        scale = 2.0
                                    }
                                }
                            }
                    )
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            }
        }
        .onAppear {
            loadPhoto()
        }
    }

    /// Loads the photo from the media item
    private func loadPhoto() {
        isLoading = true
        errorMessage = nil

        Task {
            do {
                // Try to load from PHAsset first
                if let assetIdentifier = media.assetIdentifier {
                    // First try to find the asset as a shared asset
                    var asset = SharedMediaManager.shared.fetchSharedAsset(with: assetIdentifier)

                    // If not found as a shared asset, try as a local asset
                    if asset == nil {
                        let fetchResult = PHAsset.fetchAssets(withLocalIdentifiers: [assetIdentifier], options: nil)
                        asset = fetchResult.firstObject
                    }

                    if let asset = asset, asset.mediaType == .image {
                        let options = PHImageRequestOptions()
                        options.version = .current
                        options.deliveryMode = .highQualityFormat
                        options.isNetworkAccessAllowed = true
                        options.isSynchronous = false

                        let image = await withCheckedContinuation { continuation in
                            PHImageManager.default().requestImage(
                                for: asset,
                                targetSize: PHImageManagerMaximumSize,
                                contentMode: .aspectFit,
                                options: options
                            ) { result, _ in
                                continuation.resume(returning: result)
                            }
                        }

                        if let image = image {
                            DispatchQueue.main.async {
                                self.image = image
                                self.isLoading = false
                            }
                            return
                        }
                    }
                }

                // JumpMedia doesn't have fileURL anymore, we only use assetIdentifier

                // If we couldn't load the image, throw an error
                throw NSError(domain: "NativePhotoView", code: 404, userInfo: [NSLocalizedDescriptionKey: "Photo not found"])
            } catch {
                DispatchQueue.main.async {
                    self.isLoading = false
                    self.errorMessage = error.localizedDescription
                }
            }
        }
    }
}

/// A view that displays a video using native iOS components
struct NativeVideoView: View {
    let media: JumpMedia
    @State private var player: AVPlayer?
    @State private var isLoading = true
    @State private var errorMessage: String?
    @State private var isPlaying = false

    var body: some View {
        ZStack {
            if isLoading {
                ProgressView()
                    .scaleEffect(1.5)
                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
            } else if let errorMessage = errorMessage {
                VStack(spacing: 16) {
                    Image(systemName: "exclamationmark.triangle")
                        .font(.system(size: 50))
                        .foregroundColor(.white)

                    Text("Error Loading Video")
                        .font(.headline)
                        .foregroundColor(.white)

                    Text(errorMessage)
                        .font(.subheadline)
                        .foregroundColor(.gray)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)

                    Button("Retry") {
                        loadVideo()
                    }
                    .padding()
                    .background(AppTheme.accentColor)
                    .foregroundColor(.white)
                    .cornerRadius(8)
                }
                .padding()
            } else if let player = player {
                ZStack {
                    VideoPlayer(player: player)
                        .onDisappear {
                            player.pause()
                            isPlaying = false
                        }

                    // Play button overlay when video is not playing
                    if !isPlaying {
                        Button(action: {
                            player.play()
                            isPlaying = true
                        }) {
                            ZStack {
                                Circle()
                                    .fill(Color.black.opacity(0.5))
                                    .frame(width: 80, height: 80)

                                Image(systemName: "play.fill")
                                    .font(.system(size: 30))
                                    .foregroundColor(.white)
                            }
                        }
                    }
                }
                // Listen for playback ended notification
                .onReceive(NotificationCenter.default.publisher(for: .AVPlayerItemDidPlayToEndTime)) { _ in
                    isPlaying = false
                    // Seek back to beginning
                    player.seek(to: CMTime.zero)
                }
            }
        }
        .onAppear {
            loadVideo()
        }
    }

    /// Loads the video from the media item
    private func loadVideo() {
        isLoading = true
        errorMessage = nil
        player = nil

        Task {
            do {
                // JumpMedia doesn't have fileURL anymore, we only use assetIdentifier

                // Try to load from PHAsset if local file failed or doesn't exist
                if let assetIdentifier = media.assetIdentifier {
                    // First try to find the asset as a shared asset
                    var asset = SharedMediaManager.shared.fetchSharedAsset(with: assetIdentifier)

                    // If not found as a shared asset, try as a local asset
                    if asset == nil {
                        let fetchResult = PHAsset.fetchAssets(withLocalIdentifiers: [assetIdentifier], options: nil)
                        asset = fetchResult.firstObject
                    }

                    if asset == nil {
                        throw NSError(domain: "NativeVideoView", code: 404,
                                     userInfo: [NSLocalizedDescriptionKey: "Video not found in Photos library"])
                    }

                    guard let asset = asset else {
                        throw NSError(domain: "NativeVideoView", code: 404,
                                     userInfo: [NSLocalizedDescriptionKey: "Failed to access video in Photos library"])
                    }

                    if asset.mediaType != .video {
                        throw NSError(domain: "NativeVideoView", code: 415,
                                     userInfo: [NSLocalizedDescriptionKey: "Media is not a video"])
                    }

                    let options = PHVideoRequestOptions()
                    options.version = .current
                    options.deliveryMode = .highQualityFormat
                    options.isNetworkAccessAllowed = true

                    // Request the AVAsset
                    let (avAsset, audioMix) = await withCheckedContinuation { continuation in
                        PHImageManager.default().requestAVAsset(forVideo: asset, options: options) { avAsset, audioMix, info in
                            continuation.resume(returning: (avAsset, audioMix))
                        }
                    }

                    if let avAsset = avAsset {
                        // Create player item and player
                        let playerItem = AVPlayerItem(asset: avAsset)
                        if let audioMix = audioMix {
                            playerItem.audioMix = audioMix
                        }

                        let player = AVPlayer(playerItem: playerItem)

                        // Set the playback time if specified
                        let posterTime = media.posterTime
                        if posterTime > 0 {
                            Task {
                                await player.seek(to: CMTime(seconds: posterTime, preferredTimescale: 600))
                            }
                        }

                        DispatchQueue.main.async {
                            self.player = player
                            self.isLoading = false
                        }
                        return
                    }
                }

                // If we couldn't load the video, throw an error
                throw NSError(domain: "NativeVideoView", code: 404, userInfo: [NSLocalizedDescriptionKey: "Video not found or not playable"])
            } catch {
                DispatchQueue.main.async {
                    self.isLoading = false
                    self.errorMessage = error.localizedDescription
                }
            }
        }
    }
}
