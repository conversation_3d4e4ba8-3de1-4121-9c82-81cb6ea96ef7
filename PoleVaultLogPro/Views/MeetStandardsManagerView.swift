import SwiftUI

// MARK: - Sheet Presentation Models
struct AddStandardSheetItem: Identifiable {
    let id = UUID()
}

struct ImportCSVSheetItem: Identifiable {
    let id = UUID()
}

struct NFHSConfigSheetItem: Identifiable {
    let id = UUID()
}

struct MeetStandardsManagerView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var standardsManager = MeetStandardsManager.shared

    @State private var addStandardSheet: AddStandardSheetItem?
    @State private var importCSVSheet: ImportCSVSheetItem?
    @State private var nfhsConfigSheet: NFHSConfigSheetItem?
    @State private var csvText = ""
    @State private var showingAlert = false
    @State private var alertTitle = ""
    @State private var alertMessage = ""

    var body: some View {
        NavigationStack {
            List {
                Section(header: Text("Built-in Standards")) {
                    // NFHS Standard with configuration option
                    HStack {
                        standardRow(standardsManager.nfhsStandard)

                        Button(action: {
                            nfhsConfigSheet = NFHSConfigSheetItem()
                        }) {
                            Image(systemName: "gearshape")
                                .foregroundColor(.blue)
                        }
                        .buttonStyle(BorderlessButtonStyle())
                    }

                    // FHSAA Standard
                    standardRow(MeetStandardsManager.fhsaaStandard)

                    // ALL Standard (equivalent to practice mode)
                    standardRow(MeetStandardsManager.allStandard)
                }

                Section(header: Text("Custom Standards")) {
                    if standardsManager.customStandards.isEmpty {
                        Text("No custom standards")
                            .foregroundColor(.secondary)
                            .italic()
                    } else {
                        ForEach(standardsManager.customStandards) { standard in
                            NavigationLink(destination: StandardDetailView(standard: standard)) {
                                standardRow(standard)
                            }
                            .swipeActions {
                                Button(role: .destructive) {
                                    standardsManager.deleteCustomStandard(id: standard.id)
                                } label: {
                                    Label("Delete", systemImage: "trash")
                                }
                            }
                        }
                    }
                }

                Section {
                    Button("Add Custom Standard") {
                        addStandardSheet = AddStandardSheetItem()
                    }

                    Button("Import from CSV") {
                        importCSVSheet = ImportCSVSheetItem()
                    }
                }
            }
            .navigationTitle("Meet Standards")
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
            .sheet(item: $addStandardSheet) { _ in
                AddStandardView()
            }
            .sheet(item: $importCSVSheet) { _ in
                ImportCSVView(csvText: $csvText) { success in
                    if success {
                        if let standard = standardsManager.importFromCSV(csvText) {
                            standardsManager.addCustomStandard(standard)
                            alertTitle = "Success"
                            alertMessage = "Standard '\(standard.name)' imported successfully."
                        } else {
                            alertTitle = "Error"
                            alertMessage = "Failed to parse CSV. Please check the format."
                        }
                        showingAlert = true
                    }
                }
            }
            .sheet(item: $nfhsConfigSheet) { _ in
                NFHSConfigView()
            }
            .alert(alertTitle, isPresented: $showingAlert) {
                Button("OK") { }
            } message: {
                Text(alertMessage)
            }
        }
    }

    private func standardRow(_ standard: MeetStandard) -> some View {
        HStack {
            VStack(alignment: .leading) {
                Text(standard.name)
                    .font(.headline)
                Text(standard.description)
                    .font(.caption)
                    .foregroundColor(.secondary)

                if standard.isDynamic && standard.id == "NFHS" {
                    Text("Starting: \(Int(standard.startingHeight)) cm, Increment: \(Int(standard.increment)) cm")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            Spacer()

            if standardsManager.defaultStandardId == standard.id {
                Text("Default")
                    .font(.caption)
                    .foregroundColor(.white)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(AppTheme.accentColor)
                    .cornerRadius(8)
            }
        }
        .contentShape(Rectangle())
        .onTapGesture {
            standardsManager.setDefaultStandard(standard.id)
        }
    }
}

struct StandardDetailView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var standardsManager = MeetStandardsManager.shared

    let standard: MeetStandard
    @State private var editedName: String
    @State private var editedDescription: String
    @State private var editedHeights: [Double]
    @State private var newHeight: String = ""

    init(standard: MeetStandard) {
        self.standard = standard
        _editedName = State(initialValue: standard.name)
        _editedDescription = State(initialValue: standard.description)
        _editedHeights = State(initialValue: standard.heights.sorted())
    }

    var body: some View {
        Form {
            Section(header: Text("Standard Details")) {
                TextField("Name", text: $editedName)
                TextField("Description", text: $editedDescription)
            }

            Section(header: Text("Heights")) {
                ForEach(editedHeights.indices, id: \.self) { index in
                    HStack {
                        if AppTheme.useMetricSystem {
                            Text("\(Int(editedHeights[index])) cm")
                                .fontWeight(.bold)
                            Text("(\(HeightConverter.cmToFeetInchesString(editedHeights[index])))")
                                .foregroundColor(.secondary)
                                .font(.footnote)
                        } else {
                            Text(HeightConverter.cmToFeetInchesString(editedHeights[index]))
                                .fontWeight(.bold)
                            Text("(\(Int(editedHeights[index])) cm)")
                                .foregroundColor(.secondary)
                                .font(.footnote)
                        }
                    }
                    .swipeActions {
                        Button(role: .destructive) {
                            editedHeights.remove(at: index)
                        } label: {
                            Label("Delete", systemImage: "trash")
                        }
                    }
                }

                HStack {
                    TextField("New Height (cm)", text: $newHeight)
                        .keyboardType(.decimalPad)

                    Button(action: {
                        if let height = Double(newHeight), height > 0 {
                            editedHeights.append(height)
                            editedHeights.sort()
                            newHeight = ""
                        }
                    }) {
                        Image(systemName: "plus.circle.fill")
                    }
                    .disabled(newHeight.isEmpty)
                }
            }

            Section {
                Button("Save Changes") {
                    let updatedStandard = MeetStandard(
                        id: standard.id,
                        name: editedName,
                        description: editedDescription,
                        isBuiltIn: standard.isBuiltIn,
                        heights: editedHeights
                    )
                    standardsManager.updateCustomStandard(updatedStandard)
                    dismiss()
                }
                .disabled(editedName.isEmpty || editedHeights.isEmpty)
            }
        }
        .navigationTitle("Edit Standard")
    }
}

struct AddStandardView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var standardsManager = MeetStandardsManager.shared

    @State private var name = ""
    @State private var description = ""
    @State private var heights: [Double] = []
    @State private var newHeight = ""

    var body: some View {
        NavigationStack {
            Form {
                Section(header: Text("Standard Details")) {
                    TextField("Name", text: $name)
                    TextField("Description", text: $description)
                }

                Section(header: Text("Heights")) {
                    if heights.isEmpty {
                        Text("No heights added")
                            .foregroundColor(.secondary)
                            .italic()
                    } else {
                        ForEach(heights.sorted().indices, id: \.self) { index in
                            let sortedHeights = heights.sorted()
                            HStack {
                                if AppTheme.useMetricSystem {
                                    Text("\(Int(sortedHeights[index])) cm")
                                        .fontWeight(.bold)
                                    Text("(\(HeightConverter.cmToFeetInchesString(sortedHeights[index])))")
                                        .foregroundColor(.secondary)
                                        .font(.footnote)
                                } else {
                                    Text(HeightConverter.cmToFeetInchesString(sortedHeights[index]))
                                        .fontWeight(.bold)
                                    Text("(\(Int(sortedHeights[index])) cm)")
                                        .foregroundColor(.secondary)
                                        .font(.footnote)
                                }
                            }
                            .swipeActions {
                                Button(role: .destructive) {
                                    heights.remove(at: heights.firstIndex(of: sortedHeights[index])!)
                                } label: {
                                    Label("Delete", systemImage: "trash")
                                }
                            }
                        }
                    }

                    HStack {
                        TextField("New Height (cm)", text: $newHeight)
                            .keyboardType(.decimalPad)

                        Button(action: {
                            if let height = Double(newHeight), height > 0 {
                                heights.append(height)
                                newHeight = ""
                            }
                        }) {
                            Image(systemName: "plus.circle.fill")
                        }
                        .disabled(newHeight.isEmpty)
                    }
                }
            }
            .navigationTitle("Add Standard")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("Cancel") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .confirmationAction) {
                    Button("Save") {
                        let newStandard = MeetStandard(
                            id: UUID().uuidString,
                            name: name,
                            description: description,
                            isBuiltIn: false,
                            heights: heights
                        )
                        standardsManager.addCustomStandard(newStandard)
                        dismiss()
                    }
                    .disabled(name.isEmpty || heights.isEmpty)
                }
            }
        }
    }
}

struct ImportCSVView: View {
    @Environment(\.dismiss) private var dismiss
    @Binding var csvText: String
    let onImport: (Bool) -> Void

    var body: some View {
        NavigationStack {
            VStack {
                Text("Import CSV Format:")
                    .font(.headline)
                    .padding(.top)

                Text("Line 1: Standard Name\nLine 2: Comma-separated heights in cm\n\nExample:\nNFHS Standard\n244,274,305,335,366,396,427,457,488,518,549,579,610")
                    .font(.caption)
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
                    .padding(.horizontal)

                TextEditor(text: $csvText)
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
                    .padding(.horizontal)
            }
            .navigationTitle("Import from CSV")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("Cancel") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .confirmationAction) {
                    Button("Import") {
                        onImport(true)
                        dismiss()
                    }
                    .disabled(csvText.isEmpty)
                }
            }
        }
    }
}

#Preview {
    MeetStandardsManagerView()
}
