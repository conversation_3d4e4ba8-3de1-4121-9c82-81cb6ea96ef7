import SwiftUI

struct ConfettiView: View {
    @Binding var isShowing: Bool
    let duration: Double
    
    @State private var particles: [Particle] = []
    @State private var timer: Timer?
    
    init(isShowing: Binding<Bool>, duration: Double = 3.0) {
        self._isShowing = isShowing
        self.duration = duration
    }
    
    var body: some View {
        ZStack {
            ForEach(particles) { particle in
                ConfettiParticle(position: particle.position, color: particle.color, rotation: particle.rotation, size: particle.size)
            }
        }
        .onChange(of: isShowing) { _, newValue in
            if newValue {
                startConfetti()
                
                // Auto-hide after duration
                DispatchQueue.main.asyncAfter(deadline: .now() + duration) {
                    withAnimation {
                        isShowing = false
                    }
                }
            } else {
                stopConfetti()
            }
        }
    }
    
    private func startConfetti() {
        // Generate initial particles
        particles = (0..<100).map { _ in
            Particle(
                position: CGPoint(
                    x: CGFloat.random(in: 0...UIScreen.main.bounds.width),
                    y: -50
                ),
                color: [.blue, .green, .red, .yellow, .orange, .purple].randomElement()!,
                rotation: Double.random(in: 0...360),
                size: CGFloat.random(in: 5...15)
            )
        }
        
        // Animate particles
        timer = Timer.scheduledTimer(withTimeInterval: 0.05, repeats: true) { _ in
            withAnimation(.linear(duration: 0.05)) {
                for i in 0..<particles.count {
                    // Move down and sideways
                    particles[i].position.y += CGFloat.random(in: 5...15)
                    particles[i].position.x += CGFloat.random(in: -5...5)
                    
                    // Rotate
                    particles[i].rotation += Double.random(in: -10...10)
                    
                    // Reset particles that go off screen
                    if particles[i].position.y > UIScreen.main.bounds.height + 50 {
                        particles[i].position = CGPoint(
                            x: CGFloat.random(in: 0...UIScreen.main.bounds.width),
                            y: -50
                        )
                    }
                }
            }
        }
    }
    
    private func stopConfetti() {
        timer?.invalidate()
        timer = nil
        particles = []
    }
}

struct Particle: Identifiable {
    let id = UUID()
    var position: CGPoint
    let color: Color
    var rotation: Double
    let size: CGFloat
}

struct ConfettiParticle: View {
    let position: CGPoint
    let color: Color
    let rotation: Double
    let size: CGFloat
    
    var body: some View {
        Rectangle()
            .fill(color)
            .frame(width: size, height: size)
            .position(position)
            .rotationEffect(.degrees(rotation))
    }
}

#Preview {
    ConfettiView(isShowing: .constant(true))
}
