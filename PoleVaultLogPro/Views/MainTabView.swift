import SwiftUI

struct MainTabView: View {
    @State private var selectedTab = 0
    @State private var themeRefreshTrigger = false

    var body: some View {
        TabView(selection: $selectedTab) {
            LogView()
                .tabItem {
                    Label("Log", systemImage: "list.bullet.clipboard")
                }
                .tag(0)

            DashboardView()
                .tabItem {
                    Label("Dashboard", systemImage: "chart.bar")
                }
                .tag(1)

            HistoryView()
                .tabItem {
                    Label("History", systemImage: "clock")
                }
                .tag(2)

            JumpMediaListView()
                .tabItem {
                    Label("Media", systemImage: "photo.on.rectangle")
                }
                .tag(3)

            ConverterView()
                .tabItem {
                    Label("Converter", systemImage: "ruler")
                }
                .tag(4)

            SettingsView()
                .tabItem {
                    Label("Settings", systemImage: "gear")
                }
                .tag(5)
        }
        .tint(AppTheme.accentColor) // Set the accent color for the tab bar
        .id(themeRefreshTrigger) // Force refresh when theme changes
        .onAppear {
            setupThemeChangeListener()
        }
    }

    private func setupThemeChangeListener() {
        NotificationCenter.default.addObserver(
            forName: NSNotification.Name("AppThemeChanged"),
            object: nil,
            queue: .main
        ) { _ in
            // Toggle the state to force a refresh
            themeRefreshTrigger.toggle()
        }
    }
}

#Preview {
    MainTabView()
        .environment(\.managedObjectContext, PersistenceController(inMemory: true).container.viewContext)
}
