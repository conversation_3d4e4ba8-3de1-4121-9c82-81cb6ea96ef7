import SwiftUI

struct OrphanedSessionsView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.managedObjectContext) private var viewContext
    @Environment(\.presentationMode) private var presentationMode

    let orphanedSessions: [SessionExport]
    let onComplete: (Int) -> Void

    @State private var selectedAthlete: Athlete?
    @State private var showingAlert = false
    @State private var alertTitle = ""
    @State private var alertMessage = ""
    @State private var isProcessing = false

    // Fetch all athletes
    @FetchRequest(
        sortDescriptors: [NSSortDescriptor(keyPath: \Athlete.name, ascending: true)],
        animation: .default)
    private var athletes: FetchedResults<Athlete>

    var body: some View {
        NavigationStack {
            Form {
                Section("Orphaned Sessions") {
                    Text("The following sessions have no athlete assigned:")
                        .font(.subheadline)
                        .foregroundColor(.secondary)

                    ForEach(orphanedSessions, id: \.id) { session in
                        VStack(alignment: .leading, spacing: 4) {
                            Text(session.title)
                                .font(.headline)

                            HStack {
                                Text(formattedDate(session.date))
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)

                                Spacer()

                                Text(session.type)
                                    .font(.caption)
                                    .padding(.horizontal, 6)
                                    .padding(.vertical, 2)
                                    .background(session.type == "Practice" ? Color.gray.opacity(0.2) : Color.orange.opacity(0.2))
                                    .cornerRadius(4)
                            }

                            Text("\(session.jumps.count) jumps")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        .padding(.vertical, 4)
                    }
                }

                Section("Select Athlete") {
                    if athletes.isEmpty {
                        Text("No athletes found. Please create an athlete first.")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    } else {
                        Picker("Athlete", selection: $selectedAthlete) {
                            Text("Select an athlete").tag(nil as Athlete?)
                            ForEach(athletes) { athlete in
                                Text(athlete.name ?? "Unknown").tag(athlete as Athlete?)
                            }
                        }
                        .pickerStyle(MenuPickerStyle())
                    }
                }

                Section("Actions") {
                    Button(action: importSessions) {
                        HStack {
                            Spacer()
                            if isProcessing {
                                ProgressView()
                                    .padding(.trailing, 5)
                            }
                            Text("Import Sessions")
                            Spacer()
                        }
                    }
                    .disabled(isProcessing || selectedAthlete == nil || athletes.isEmpty)

                    Button(action: {
                        dismiss()
                        onComplete(0)
                    }) {
                        HStack {
                            Spacer()
                            Text("Skip These Sessions")
                            Spacer()
                        }
                        .foregroundColor(.red)
                    }
                    .disabled(isProcessing)
                }
            }
            .navigationTitle("Assign Athlete")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("Cancel") {
                        dismiss()
                        onComplete(0)
                    }
                    .disabled(isProcessing)
                }
            }
            .alert(alertTitle, isPresented: $showingAlert) {
                Button("OK") {
                    if alertTitle == "Success" {
                        dismiss()
                    }
                }
            } message: {
                Text(alertMessage)
            }
        }
    }

    private func formattedDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        return formatter.string(from: date)
    }

    private func importSessions() {
        guard let athlete = selectedAthlete else {
            alertTitle = "Error"
            alertMessage = "Please select an athlete"
            showingAlert = true
            return
        }

        print("Starting import of \(orphanedSessions.count) orphaned sessions with athlete: \(athlete.name ?? "Unknown")")
        isProcessing = true

        do {
            let importedCount = try ExportImportManager.shared.importOrphanedSessions(
                sessions: orphanedSessions,
                athlete: athlete,
                context: viewContext
            )

            print("Successfully imported \(importedCount) orphaned sessions")
            alertTitle = "Success"
            if importedCount > 0 {
                alertMessage = "Successfully imported/updated \(importedCount) sessions"
            } else {
                alertMessage = "No sessions were imported or updated"
            }
            showingAlert = true

            // Call the completion handler with the number of imported sessions
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                // Slight delay to ensure alert is shown before dismissing
                self.dismiss()
                onComplete(importedCount)
            }
        } catch {
            print("Error importing orphaned sessions: \(error.localizedDescription)")
            alertTitle = "Error"
            alertMessage = "Failed to import sessions: \(error.localizedDescription)"
            showingAlert = true
            isProcessing = false
        }
    }
}

// Extension to make SessionExport identifiable
extension SessionExport: Identifiable {}
