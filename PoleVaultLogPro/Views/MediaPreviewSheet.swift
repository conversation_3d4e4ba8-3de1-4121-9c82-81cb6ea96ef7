import SwiftUI
import Photos
import PhotosUI
import AVKit

/// A view that displays a preview of a media item
struct MediaPreviewSheet: View {
    /// The media item to preview
    let media: JumpMediaStruct

    /// Whether the preview is being presented
    @Binding var isPresented: Bool

    /// The action to perform when the delete button is tapped
    var onDelete: (() -> Void)?

    /// State to track if the view is ready to be presented
    @State private var isViewReady = false

    /// State to track if we're in the process of dismissing
    @State private var isDismissing = false

    // State to track if the media is valid
    @State private var validatedMedia: JumpMediaStruct?
    @State private var isValidating = true
    @State private var validationError: String?

    var body: some View {
        NavigationView {
            ZStack {
                Color.black.edgesIgnoringSafeArea(.all)

                if isViewReady {
                    if let validationError = validationError {
                        // Show error message if validation failed
                        VStack(spacing: 16) {
                            Image(systemName: "exclamationmark.triangle")
                                .font(.system(size: 50))
                                .foregroundColor(.white)

                            Text("Media Not Available")
                                .font(.headline)
                                .foregroundColor(.white)

                            Text(validationError)
                                .font(.subheadline)
                                .foregroundColor(.gray)
                                .multilineTextAlignment(.center)
                                .padding(.horizontal)
                        }
                    } else if let validatedMedia = validatedMedia {
                        if validatedMedia.type == .video {
                            VideoPlayerView(media: validatedMedia)
                        } else {
                            PhotoView(media: validatedMedia)
                        }
                    } else if isValidating {
                        // Show a loading indicator while validating
                        ProgressView("Validating media...")
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .foregroundColor(.white)
                    } else {
                        // Show a loading indicator while preparing the view
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    }
                } else {
                    // Show a loading indicator while preparing the view
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                }
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: {
                        dismissSafely()
                    }) {
                        Image(systemName: "xmark")
                            .foregroundColor(.white)
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        if let onDelete = onDelete {
                            onDelete()
                        }
                        dismissSafely()
                    }) {
                        Image(systemName: "trash")
                            .foregroundColor(.white)
                    }
                }
            }
            .onAppear {
                // Mark the view as ready after a short delay
                // This helps avoid presentation conflicts
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                    isViewReady = true

                    // Validate the media
                    Task {
                        // Check if the media is available
                        let (isAvailable, repairedMedia) = media.validateMedia()

                        DispatchQueue.main.async {
                            isValidating = false

                            if isAvailable {
                                // Use repaired media if available
                                validatedMedia = repairedMedia ?? media
                            } else {
                                // Show error message
                                validationError = "The media file could not be found. It may have been deleted or moved."
                            }
                        }
                    }
                }
            }
        }
    }

    /// Safely dismisses the sheet with a small delay to avoid presentation conflicts
    private func dismissSafely() {
        // Prevent multiple dismissals
        if isDismissing {
            return
        }

        print("Sheet dismissal started")
        isDismissing = true

        // Hide content first
        isViewReady = false

        // Then dismiss after a longer delay to ensure any animations complete
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            print("Sheet dismissed")
            isPresented = false

            // Reset the flag after an additional delay to prevent rapid re-presentations
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                isDismissing = false
            }
        }
    }
}

/// A view that displays a grid of media items
struct MediaGridView: View {
    /// The media items to display
    let mediaItems: [JumpMediaStruct]

    /// The number of columns in the grid
    var columns: Int = 3

    /// The spacing between items
    var spacing: CGFloat = 8

    /// The action to perform when an item is tapped
    var onTap: ((JumpMediaStruct) -> Void)?

    /// The action to perform when an item is deleted
    var onDelete: ((JumpMediaStruct) -> Void)?

    /// Whether to show the media type icon
    var showTypeIcon: Bool = true

    /// The size of each thumbnail
    var thumbnailSize: CGSize = CGSize(width: 100, height: 100)

    /// Whether the grid is empty
    private var isEmpty: Bool {
        mediaItems.isEmpty
    }

    var body: some View {
        Group {
            if isEmpty {
                emptyStateView
            } else {
                mediaGridView
            }
        }
    }

    /// The empty state view
    private var emptyStateView: some View {
        VStack(spacing: 16) {
            PoleVaultIcon(size: CGSize(width: 60, height: 60))
                .opacity(0.7)

            Text("No Media")
                .font(.headline)

            Text("Tap the + button to add photos or videos")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemGroupedBackground))
    }

    /// The media grid view
    private var mediaGridView: some View {
        let gridItems = Array(repeating: GridItem(.flexible(), spacing: spacing), count: columns)

        return ScrollView {
            LazyVGrid(columns: gridItems, spacing: spacing) {
                ForEach(mediaItems) { media in
                    MediaThumbnailView(
                        media: media,
                        size: thumbnailSize,
                        showTypeIcon: showTypeIcon
                    )
                    .contextMenu {
                        Button(action: {
                            onTap?(media)
                        }) {
                            Label("View", systemImage: "eye")
                        }

                        Button(role: .destructive, action: {
                            onDelete?(media)
                        }) {
                            Label("Delete", systemImage: "trash")
                        }
                    }
                    .onTapGesture {
                        onTap?(media)
                    }
                }
            }
            .padding(.horizontal)
            .padding(.vertical, spacing)
        }
    }
}

/// A view that allows adding media to a jump
struct MediaPickerView: View {
    /// The managed object context
    @Environment(\.managedObjectContext) private var viewContext

    /// The jump to add media to
    let jump: Jump

    /// Whether the picker is being presented
    @Binding var isPresented: Bool

    /// The selected items from the photo picker
    @State private var selectedItems: [PhotosPickerItem] = []

    /// Whether media is being loaded
    @State private var isLoading = false

    /// The media items attached to the jump
    @State private var mediaItems: [JumpMediaStruct] = []

    /// The media item being previewed
    @State private var previewMedia: JumpMediaStruct?

    var body: some View {
        NavigationView {
            VStack {
                // Media grid
                MediaGridView(
                    mediaItems: mediaItems,
                    onTap: { media in
                        previewMedia = media
                    },
                    onDelete: { media in
                        deleteMedia(media)
                    }
                )

                // Add media button
                PhotosPicker(
                    selection: $selectedItems,
                    matching: .any(of: [.images, .videos]),
                    photoLibrary: .shared()
                ) {
                    Label("Add Media", systemImage: "plus")
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(AppTheme.accentColor)
                        .foregroundColor(.white)
                        .cornerRadius(8)
                        .padding(.horizontal)
                }
                .onChange(of: selectedItems) { oldValue, newValue in
                    processSelectedItems()
                }

                if isLoading {
                    ProgressView("Processing media...")
                        .padding()
                }
            }
            .navigationTitle("Media")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        isPresented = false
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        isPresented = false
                    }
                }
            }
            .sheet(item: $previewMedia) { media in
                MediaPreviewSheet(
                    media: media,
                    isPresented: Binding(
                        get: { previewMedia != nil },
                        set: { if !$0 {
                            // Add a small delay before setting previewMedia to nil
                            // to avoid presentation conflicts
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                                previewMedia = nil
                            }
                        } }
                    ),
                    onDelete: {
                        deleteMedia(media)
                    }
                )
            }
            .onAppear {
                loadMediaItems()
            }
        }
    }

    /// Loads the media items attached to the jump
    private func loadMediaItems() {
        mediaItems = jump.getMediaItemsAsStructs()
    }

    /// Processes the selected items from the photo picker
    private func processSelectedItems() {
        guard !selectedItems.isEmpty else { return }

        isLoading = true

        Task {
            // Process each selected item
            for item in selectedItems {
                if let media = await MediaStorageManager.shared.processMediaItem(item) {
                    DispatchQueue.main.async {
                        // Add the media to the jump
                        let success = jump.addMediaStruct(media)

                        if success {
                            // Save the context
                            do {
                                try viewContext.save()
                                print("Successfully saved jump with new media")

                                // Reload the media items
                                loadMediaItems()
                            } catch {
                                print("Error saving jump with new media: \(error)")
                            }
                        }
                    }
                }
            }

            DispatchQueue.main.async {
                isLoading = false
                selectedItems = []
            }
        }
    }

    /// Deletes a media item
    private func deleteMedia(_ media: JumpMediaStruct) {
        // Remove the media from the jump
        let success = jump.removeMedia(withId: media.id)

        if success {
            // Save the context
            do {
                try viewContext.save()
                print("Successfully removed media from jump")

                // Reload the media items
                loadMediaItems()
            } catch {
                print("Error removing media from jump: \(error)")
            }
        }
    }
}

#Preview {
    MediaPreviewSheet(
        media: JumpMediaStruct(
            type: .video,
            assetIdentifier: "test-asset-id"
        ),
        isPresented: .constant(true)
    )
}
