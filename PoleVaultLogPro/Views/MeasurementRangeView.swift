import SwiftUI

struct MeasurementRangeView: View {
    @Environment(\.dismiss) private var dismiss

    // Measurement ranges
    @AppStorage("runStartMinCm") private var runStartMinCm: Double = 0
    @AppStorage("runStartMaxCm") private var runStartMaxCm: Double = 3048

    @AppStorage("handHoldMinCm") private var handHoldMinCm: Double = 213
    @AppStorage("handHoldMaxCm") private var handHoldMaxCm: Double = 487

    @AppStorage("takeOffStepMinCm") private var takeOffStepMinCm: Double = 152
    @AppStorage("takeOffStepMaxCm") private var takeOffStepMaxCm: Double = 487

    @AppStorage("standardMinCm") private var standardMinCm: Double = 30
    @AppStorage("standardMaxCm") private var standardMaxCm: Double = 80

    @AppStorage("barHeightMinCm") private var barHeightMinCm: Double = 25 // 0.25m in cm
    @AppStorage("barHeightMaxCm") private var barHeightMaxCm: Double = 700 // 7.00m in cm

    // State for editing
    @State private var editingRunStartMin: Double
    @State private var editingRunStartMax: Double
    @State private var editingHandHoldMin: Double
    @State private var editingHandHoldMax: Double
    @State private var editingTakeOffStepMin: Double
    @State private var editingTakeOffStepMax: Double
    @State private var editingStandardMin: Double
    @State private var editingStandardMax: Double
    @State private var editingBarHeightMin: Double
    @State private var editingBarHeightMax: Double

    // For showing reset confirmation
    @State private var showingResetAlert = false

    init() {
        // Initialize state variables with stored values
        _editingRunStartMin = State(initialValue: UserDefaults.standard.double(forKey: "runStartMinCm"))
        _editingRunStartMax = State(initialValue: UserDefaults.standard.double(forKey: "runStartMaxCm"))
        _editingHandHoldMin = State(initialValue: UserDefaults.standard.double(forKey: "handHoldMinCm"))
        _editingHandHoldMax = State(initialValue: UserDefaults.standard.double(forKey: "handHoldMaxCm"))
        _editingTakeOffStepMin = State(initialValue: UserDefaults.standard.double(forKey: "takeOffStepMinCm"))
        _editingTakeOffStepMax = State(initialValue: UserDefaults.standard.double(forKey: "takeOffStepMaxCm"))
        _editingStandardMin = State(initialValue: UserDefaults.standard.double(forKey: "standardMinCm"))
        _editingStandardMax = State(initialValue: UserDefaults.standard.double(forKey: "standardMaxCm"))
        _editingBarHeightMin = State(initialValue: UserDefaults.standard.double(forKey: "barHeightMinCm"))
        _editingBarHeightMax = State(initialValue: UserDefaults.standard.double(forKey: "barHeightMaxCm"))

        // Set defaults if values are 0 (not yet set)
        if _editingRunStartMin.wrappedValue == 0 && _editingRunStartMax.wrappedValue == 0 {
            _editingRunStartMin = State(initialValue: 0)
            _editingRunStartMax = State(initialValue: 3048)
        }

        if _editingHandHoldMin.wrappedValue == 0 && _editingHandHoldMax.wrappedValue == 0 {
            _editingHandHoldMin = State(initialValue: 213)
            _editingHandHoldMax = State(initialValue: 487)
        }

        if _editingTakeOffStepMin.wrappedValue == 0 && _editingTakeOffStepMax.wrappedValue == 0 {
            _editingTakeOffStepMin = State(initialValue: 152)
            _editingTakeOffStepMax = State(initialValue: 487)
        }

        if _editingStandardMin.wrappedValue == 0 && _editingStandardMax.wrappedValue == 0 {
            _editingStandardMin = State(initialValue: 30)
            _editingStandardMax = State(initialValue: 80)
        }

        if _editingBarHeightMin.wrappedValue == 0 && _editingBarHeightMax.wrappedValue == 0 {
            _editingBarHeightMin = State(initialValue: 91)
            _editingBarHeightMax = State(initialValue: 609)
        }
    }

    var body: some View {
        NavigationStack {
            Form {
                Section(header: Text("Run Start")) {
                    HStack {
                        Text("Minimum")
                        Spacer()

                        // Show both units with default unit highlighted
                        if AppTheme.useMetricSystem {
                            Text("\(Int(editingRunStartMin)) cm")
                                .fontWeight(.bold)
                            Text("(\(HeightConverter.formatStepMeasurement(cm: editingRunStartMin)))")
                                .foregroundColor(.secondary)
                                .font(.footnote)
                        } else {
                            Text(HeightConverter.formatStepMeasurement(cm: editingRunStartMin))
                                .fontWeight(.bold)
                            Text("(\(Int(editingRunStartMin)) cm)")
                                .foregroundColor(.secondary)
                                .font(.footnote)
                        }
                    }

                    Slider(
                        value: $editingRunStartMin,
                        in: 0...editingRunStartMax,
                        step: AppTheme.useMetricSystem ? 1 : HeightConverter.inchesToCm(1.0)
                    )
                    .onChange(of: editingRunStartMin) { _, newValue in
                        if !AppTheme.useMetricSystem {
                            // Round to nearest whole-inch when using imperial
                            editingRunStartMin = HeightConverter.roundToWholeInchInCm(newValue)
                        }
                    }

                    HStack {
                        Text("Maximum")
                        Spacer()

                        if AppTheme.useMetricSystem {
                            Text("\(Int(editingRunStartMax)) cm")
                                .fontWeight(.bold)
                            Text("(\(HeightConverter.formatStepMeasurement(cm: editingRunStartMax)))")
                                .foregroundColor(.secondary)
                                .font(.footnote)
                        } else {
                            Text(HeightConverter.formatStepMeasurement(cm: editingRunStartMax))
                                .fontWeight(.bold)
                            Text("(\(Int(editingRunStartMax)) cm)")
                                .foregroundColor(.secondary)
                                .font(.footnote)
                        }
                    }

                    Slider(
                        value: $editingRunStartMax,
                        in: editingRunStartMin...3048,
                        step: AppTheme.useMetricSystem ? 1 : HeightConverter.inchesToCm(1.0)
                    )
                    .onChange(of: editingRunStartMax) { _, newValue in
                        if !AppTheme.useMetricSystem {
                            // Round to nearest whole-inch when using imperial
                            editingRunStartMax = HeightConverter.roundToWholeInchInCm(newValue)
                        }
                    }
                }

                Section(header: Text("Hand Hold")) {
                    HStack {
                        Text("Minimum")
                        Spacer()

                        if AppTheme.useMetricSystem {
                            Text("\(Int(editingHandHoldMin)) cm")
                                .fontWeight(.bold)
                            Text("(\(HeightConverter.formatHandHoldMeasurement(cm: editingHandHoldMin)))")
                                .foregroundColor(.secondary)
                                .font(.footnote)
                        } else {
                            Text(HeightConverter.formatHandHoldMeasurement(cm: editingHandHoldMin))
                                .fontWeight(.bold)
                            Text("(\(Int(editingHandHoldMin)) cm)")
                                .foregroundColor(.secondary)
                                .font(.footnote)
                        }
                    }

                    Slider(
                        value: $editingHandHoldMin,
                        in: 0...editingHandHoldMax,
                        step: AppTheme.useMetricSystem ? 1 : HeightConverter.inchesToCm(1.0)
                    )
                    .onChange(of: editingHandHoldMin) { _, newValue in
                        if !AppTheme.useMetricSystem {
                            // Round to nearest whole-inch when using imperial
                            editingHandHoldMin = HeightConverter.roundToWholeInchInCm(newValue)
                        }
                    }

                    HStack {
                        Text("Maximum")
                        Spacer()

                        if AppTheme.useMetricSystem {
                            Text("\(Int(editingHandHoldMax)) cm")
                                .fontWeight(.bold)
                            Text("(\(HeightConverter.formatHandHoldMeasurement(cm: editingHandHoldMax)))")
                                .foregroundColor(.secondary)
                                .font(.footnote)
                        } else {
                            Text(HeightConverter.formatHandHoldMeasurement(cm: editingHandHoldMax))
                                .fontWeight(.bold)
                            Text("(\(Int(editingHandHoldMax)) cm)")
                                .foregroundColor(.secondary)
                                .font(.footnote)
                        }
                    }

                    Slider(
                        value: $editingHandHoldMax,
                        in: editingHandHoldMin...600,
                        step: AppTheme.useMetricSystem ? 1 : HeightConverter.inchesToCm(1.0)
                    )
                    .onChange(of: editingHandHoldMax) { _, newValue in
                        if !AppTheme.useMetricSystem {
                            // Round to nearest whole-inch when using imperial
                            editingHandHoldMax = HeightConverter.roundToWholeInchInCm(newValue)
                        }
                    }
                }

                Section(header: Text("Take-Off Step")) {
                    HStack {
                        Text("Minimum")
                        Spacer()

                        if AppTheme.useMetricSystem {
                            Text("\(Int(editingTakeOffStepMin)) cm")
                                .fontWeight(.bold)
                            Text("(\(HeightConverter.formatStepMeasurement(cm: editingTakeOffStepMin)))")
                                .foregroundColor(.secondary)
                                .font(.footnote)
                        } else {
                            Text(HeightConverter.formatStepMeasurement(cm: editingTakeOffStepMin))
                                .fontWeight(.bold)
                            Text("(\(Int(editingTakeOffStepMin)) cm)")
                                .foregroundColor(.secondary)
                                .font(.footnote)
                        }
                    }

                    Slider(
                        value: $editingTakeOffStepMin,
                        in: 0...editingTakeOffStepMax,
                        step: AppTheme.useMetricSystem ? 1 : HeightConverter.inchesToCm(1.0)
                    )
                    .onChange(of: editingTakeOffStepMin) { _, newValue in
                        if !AppTheme.useMetricSystem {
                            // Round to nearest whole-inch when using imperial
                            editingTakeOffStepMin = HeightConverter.roundToWholeInchInCm(newValue)
                        }
                    }

                    HStack {
                        Text("Maximum")
                        Spacer()

                        if AppTheme.useMetricSystem {
                            Text("\(Int(editingTakeOffStepMax)) cm")
                                .fontWeight(.bold)
                            Text("(\(HeightConverter.formatStepMeasurement(cm: editingTakeOffStepMax)))")
                                .foregroundColor(.secondary)
                                .font(.footnote)
                        } else {
                            Text(HeightConverter.formatStepMeasurement(cm: editingTakeOffStepMax))
                                .fontWeight(.bold)
                            Text("(\(Int(editingTakeOffStepMax)) cm)")
                                .foregroundColor(.secondary)
                                .font(.footnote)
                        }
                    }

                    Slider(
                        value: $editingTakeOffStepMax,
                        in: editingTakeOffStepMin...600,
                        step: AppTheme.useMetricSystem ? 1 : HeightConverter.inchesToCm(1.0)
                    )
                    .onChange(of: editingTakeOffStepMax) { _, newValue in
                        if !AppTheme.useMetricSystem {
                            // Round to nearest whole-inch when using imperial
                            editingTakeOffStepMax = HeightConverter.roundToWholeInchInCm(newValue)
                        }
                    }
                }

                Section(header: Text("Standard")) {
                    HStack {
                        Text("Minimum")
                        Spacer()

                        if AppTheme.useMetricSystem {
                            Text("\(Int(editingStandardMin)) cm")
                                .fontWeight(.bold)
                            Text("(\(HeightConverter.formatStandardMeasurement(cm: editingStandardMin)))")
                                .foregroundColor(.secondary)
                                .font(.footnote)
                        } else {
                            Text(HeightConverter.formatStandardMeasurement(cm: editingStandardMin))
                                .fontWeight(.bold)
                            Text("(\(Int(editingStandardMin)) cm)")
                                .foregroundColor(.secondary)
                                .font(.footnote)
                        }
                    }

                    Slider(
                        value: $editingStandardMin,
                        in: 0...editingStandardMax,
                        step: AppTheme.useMetricSystem ? 1 : HeightConverter.inchesToCm(0.25)
                    )
                    .onChange(of: editingStandardMin) { _, newValue in
                        if !AppTheme.useMetricSystem {
                            // Round to nearest quarter-inch when using imperial
                            editingStandardMin = HeightConverter.roundToQuarterInchInCm(newValue)
                        }
                    }

                    HStack {
                        Text("Maximum")
                        Spacer()

                        if AppTheme.useMetricSystem {
                            Text("\(Int(editingStandardMax)) cm")
                                .fontWeight(.bold)
                            Text("(\(HeightConverter.formatStandardMeasurement(cm: editingStandardMax)))")
                                .foregroundColor(.secondary)
                                .font(.footnote)
                        } else {
                            Text(HeightConverter.formatStandardMeasurement(cm: editingStandardMax))
                                .fontWeight(.bold)
                            Text("(\(Int(editingStandardMax)) cm)")
                                .foregroundColor(.secondary)
                                .font(.footnote)
                        }
                    }

                    Slider(
                        value: $editingStandardMax,
                        in: editingStandardMin...200,
                        step: AppTheme.useMetricSystem ? 1 : HeightConverter.inchesToCm(0.25)
                    )
                    .onChange(of: editingStandardMax) { _, newValue in
                        if !AppTheme.useMetricSystem {
                            // Round to nearest quarter-inch when using imperial
                            editingStandardMax = HeightConverter.roundToQuarterInchInCm(newValue)
                        }
                    }
                }

                Section(header: Text("Bar Height")) {
                    HStack {
                        Text("Minimum")
                        Spacer()

                        if AppTheme.useMetricSystem {
                            Text(HeightConverter.cmToMetersString(editingBarHeightMin))
                                .fontWeight(.bold)
                            Text("•")
                                .foregroundColor(.secondary)
                            Text(HeightConverter.cmToFeetInchesString(editingBarHeightMin))
                                .foregroundColor(.secondary)
                                .font(.footnote)
                        } else {
                            Text(HeightConverter.cmToFeetInchesString(editingBarHeightMin))
                                .fontWeight(.bold)
                            Text("•")
                                .foregroundColor(.secondary)
                            Text(HeightConverter.cmToMetersString(editingBarHeightMin))
                                .foregroundColor(.secondary)
                                .font(.footnote)
                        }
                    }

                    Slider(
                        value: $editingBarHeightMin,
                        in: 0...editingBarHeightMax,
                        step: AppTheme.useMetricSystem ? 1 : HeightConverter.inchesToCm(0.25)
                    )
                    .onChange(of: editingBarHeightMin) { _, newValue in
                        if !AppTheme.useMetricSystem {
                            // Round to nearest quarter-inch when using imperial
                            editingBarHeightMin = HeightConverter.roundToQuarterInchInCm(newValue)
                        }
                    }

                    HStack {
                        Text("Maximum")
                        Spacer()

                        if AppTheme.useMetricSystem {
                            Text(HeightConverter.cmToMetersString(editingBarHeightMax))
                                .fontWeight(.bold)
                            Text("•")
                                .foregroundColor(.secondary)
                            Text(HeightConverter.cmToFeetInchesString(editingBarHeightMax))
                                .foregroundColor(.secondary)
                                .font(.footnote)
                        } else {
                            Text(HeightConverter.cmToFeetInchesString(editingBarHeightMax))
                                .fontWeight(.bold)
                            Text("•")
                                .foregroundColor(.secondary)
                            Text(HeightConverter.cmToMetersString(editingBarHeightMax))
                                .foregroundColor(.secondary)
                                .font(.footnote)
                        }
                    }

                    Slider(
                        value: $editingBarHeightMax,
                        in: editingBarHeightMin...700,
                        step: AppTheme.useMetricSystem ? 1 : HeightConverter.inchesToCm(0.25)
                    )
                    .onChange(of: editingBarHeightMax) { _, newValue in
                        if !AppTheme.useMetricSystem {
                            // Round to nearest quarter-inch when using imperial
                            editingBarHeightMax = HeightConverter.roundToQuarterInchInCm(newValue)
                        }
                    }
                }

                Section {
                    Button("Reset to Defaults") {
                        showingResetAlert = true
                    }
                    .foregroundColor(.red)
                    .frame(maxWidth: .infinity, alignment: .center)
                }
            }
            .navigationTitle("Measurement Ranges")
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        saveRanges()
                        dismiss()
                    }
                    .foregroundColor(AppTheme.accentColor)
                }

                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
            .alert("Reset to Defaults", isPresented: $showingResetAlert) {
                Button("Cancel", role: .cancel) { }
                Button("Reset", role: .destructive) {
                    resetToDefaults()
                }
            } message: {
                Text("This will reset all measurement ranges to their default values.")
            }
        }
    }

    private func saveRanges() {
        // If using imperial, round values appropriately
        if !AppTheme.useMetricSystem {
            // For Run Start and Take-Off Step, use whole-inch rounding
            runStartMinCm = HeightConverter.roundToWholeInchInCm(editingRunStartMin)
            runStartMaxCm = HeightConverter.roundToWholeInchInCm(editingRunStartMax)

            // For Hand Hold, use whole-inch rounding
            handHoldMinCm = HeightConverter.roundToWholeInchInCm(editingHandHoldMin)
            handHoldMaxCm = HeightConverter.roundToWholeInchInCm(editingHandHoldMax)

            // For Take-Off Step, use whole-inch rounding
            takeOffStepMinCm = HeightConverter.roundToWholeInchInCm(editingTakeOffStepMin)
            takeOffStepMaxCm = HeightConverter.roundToWholeInchInCm(editingTakeOffStepMax)

            // For Standard and Bar Height, keep quarter-inch rounding
            standardMinCm = HeightConverter.roundToQuarterInchInCm(editingStandardMin)
            standardMaxCm = HeightConverter.roundToQuarterInchInCm(editingStandardMax)

            barHeightMinCm = HeightConverter.roundToQuarterInchInCm(editingBarHeightMin)
            barHeightMaxCm = HeightConverter.roundToQuarterInchInCm(editingBarHeightMax)
        } else {
            // For metric, save as is (whole centimeters)
            runStartMinCm = editingRunStartMin
            runStartMaxCm = editingRunStartMax

            handHoldMinCm = editingHandHoldMin
            handHoldMaxCm = editingHandHoldMax

            takeOffStepMinCm = editingTakeOffStepMin
            takeOffStepMaxCm = editingTakeOffStepMax

            standardMinCm = editingStandardMin
            standardMaxCm = editingStandardMax

            barHeightMinCm = editingBarHeightMin
            barHeightMaxCm = editingBarHeightMax
        }
    }

    private func resetToDefaults() {
        // Reset to default values from requirements
        editingRunStartMin = 0
        editingRunStartMax = 3048

        editingHandHoldMin = 213
        editingHandHoldMax = 487

        editingTakeOffStepMin = 152
        editingTakeOffStepMax = 487

        editingStandardMin = 30
        editingStandardMax = 80

        editingBarHeightMin = 25 // 0.25m in cm
        editingBarHeightMax = 700 // 7.00m in cm
    }
}

#Preview {
    MeasurementRangeView()
}
