import SwiftUI

/// A view that displays an alert when data issues are detected
struct DataRecoveryAlertView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var isRecovering = false
    @State private var recoveryComplete = false
    @State private var errorMessage: String?

    var body: some View {
        NavigationStack {
            VStack(spacing: 20) {
                Image(systemName: "exclamationmark.triangle.fill")
                    .font(.system(size: 60))
                    .foregroundColor(.orange)
                    .padding(.top, 40)

                Text("Data Issue Detected")
                    .font(.title)
                    .bold()

                Text("We've detected an issue with your data. This could be due to a sync conflict or a Core Data error.")
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)

                if isRecovering {
                    ProgressView("Attempting to recover data...")
                        .padding()
                } else if recoveryComplete {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 40))
                        .foregroundColor(.green)
                        .padding()

                    Text("Recovery complete")
                        .font(.headline)
                } else if let errorMessage = errorMessage {
                    Text(errorMessage)
                        .foregroundColor(.red)
                        .multilineTextAlignment(.center)
                        .padding()
                }

                VStack(spacing: 16) {
                    Button(action: recoverFromCloud) {
                        Label("Recover from iCloud", systemImage: "icloud.and.arrow.down")
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.blue)
                            .foregroundColor(.white)
                            .cornerRadius(10)
                    }
                    .disabled(isRecovering || recoveryComplete)

                    Button(action: continueWithoutRecovery) {
                        Text("Continue Without Recovery")
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.gray.opacity(0.2))
                            .foregroundColor(.primary)
                            .cornerRadius(10)
                    }
                    .disabled(isRecovering)

                    if !recoveryComplete && !isRecovering {
                        Button(action: resetAllData) {
                            Label("Reset All Data", systemImage: "trash")
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(Color.red)
                                .foregroundColor(.white)
                                .cornerRadius(10)
                        }
                        .padding(.top, 20)
                    }
                }
                .padding(.horizontal)
                .padding(.top, 20)

                Spacer()
            }
            .navigationTitle("Data Recovery")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                if recoveryComplete || !isRecovering {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("Done") {
                            dismiss()
                        }
                    }
                }
            }
        }
    }

    /// Attempts to recover data from iCloud
    private func recoverFromCloud() {
        isRecovering = true
        errorMessage = nil

        // Let CloudKit handle the recovery naturally
        print("🔄 Initiating CloudKit recovery")

        // Clear any error flags to allow normal operation
        UserDefaults.standard.set(false, forKey: "needsCoreDataReset")
        UserDefaults.standard.set(false, forKey: "coreDataHasLoadError")

        // Save any pending changes to trigger sync
        let context = PersistenceController.shared.container.viewContext
        if context.hasChanges {
            do {
                try context.save()
                print("💾 Saved pending changes to initiate recovery")
            } catch {
                print("❌ Error saving context: \(error.localizedDescription)")
                errorMessage = "Error initiating recovery: \(error.localizedDescription)"
            }
        }

        // Let the system handle the sync timing
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            // Mark recovery as complete
            self.isRecovering = false
            self.recoveryComplete = true

            // Post notification that recovery was attempted
            NotificationCenter.default.post(
                name: NSNotification.Name("CloudKitRecoveryAttempted"),
                object: nil
            )
        }
    }

    /// Continues without attempting recovery
    private func continueWithoutRecovery() {
        // Clear any error flags
        UserDefaults.standard.set(false, forKey: "needsCoreDataReset")
        UserDefaults.standard.set(false, forKey: "coreDataHasLoadError")

        dismiss()
    }

    /// Resets all data
    private func resetAllData() {
        isRecovering = true
        errorMessage = nil

        // Show a confirmation alert before resetting
        let alert = UIAlertController(
            title: "Confirm Data Reset",
            message: "This will delete ALL your data and cannot be undone. Are you sure you want to proceed?",
            preferredStyle: .alert
        )

        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel) { _ in
            self.isRecovering = false
        })

        alert.addAction(UIAlertAction(title: "Reset All Data", style: .destructive) { _ in
            // Set flag to indicate we're resetting data
            UserDefaults.standard.set(true, forKey: "isResettingCoreData")

            // Reset all data
            PersistenceController.shared.resetAllData()

            // Set flag to indicate app needs restart
            UserDefaults.standard.set(true, forKey: "needsAppRestart")

            // Completion
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                self.isRecovering = false
                self.recoveryComplete = true

                // Show restart alert
                NotificationCenter.default.post(
                    name: NSNotification.Name("AppNeedsRestart"),
                    object: nil
                )
            }
        })

        // Present the alert
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let rootViewController = windowScene.windows.first?.rootViewController {
            rootViewController.present(alert, animated: true)
        } else {
            // If we can't present the alert, just cancel the operation
            self.isRecovering = false
            self.errorMessage = "Could not display confirmation dialog"
        }
    }
}

#Preview {
    DataRecoveryAlertView()
}
