import SwiftUI
import Charts

struct DashboardView: View {
    @Environment(\.managedObjectContext) private var viewContext

    @FetchRequest private var sessions: FetchedResults<Session>
    @FetchRequest private var successfulJumps: FetchedResults<Jump>

    @State private var timeRange: TimeRange = .month
    @State private var showingPersonalBest: Bool = false
    @State private var personalBestHeight: Double = 0

    enum TimeRange: String, CaseIterable, Identifiable {
        case week = "Week"
        case month = "Month"
        case year = "Year"
        case all = "All Time"

        var id: String { self.rawValue }
    }

    // State to force refresh when needed
    @State private var refreshID = UUID()

    // Listen for data reset and CloudKit sync notifications
    init() {
        // We need to use a different approach for struct views
        // The notification will be handled by the parent view controller refreshing the view

        // Initialize the sessions fetch request with explicit entity description
        _sessions = FetchRequest(fetchRequest: CoreDataFetchRequestHelper.createSessionFetchRequest(
            context: PersistenceController.shared.container.viewContext,
            sortDescriptors: [NSSortDescriptor(keyPath: \Session.date, ascending: false)]
        ))

        // Initialize the successful jumps fetch request with explicit entity description
        _successfulJumps = FetchRequest(fetchRequest: CoreDataFetchRequestHelper.createJumpFetchRequest(
            context: PersistenceController.shared.container.viewContext,
            sortDescriptors: [NSSortDescriptor(keyPath: \Jump.barHeightCm, ascending: false)],
            predicate: NSPredicate(format: "result == %@", "make")
        ))
    }

    // Setup notification observers when the view appears
    private func setupNotificationObservers() {
        // Listen for CloudKit data changes
        NotificationCenter.default.addObserver(
            forName: CloudKitSyncManager.NotificationName.cloudKitDataChanged,
            object: nil,
            queue: .main
        ) { [self] _ in
            print("DashboardView received CloudKit data change notification")
            refreshID = UUID()
        }
    }

    var body: some View {
        NavigationStack {
            ZStack {
                // Background watermark
                WatermarkView()

                ScrollView {
                    VStack(spacing: 32) {
                        // Time range picker
                        Picker("Time Range", selection: $timeRange) {
                            ForEach(TimeRange.allCases) { range in
                                Text(range.rawValue).tag(range)
                            }
                        }
                        .pickerStyle(.segmented)
                        .padding(.horizontal)

                        // Personal best card (if available)
                        if let bestJump = successfulJumps.first, bestJump.barHeightCm > 0 {
                            PersonalBestCard(jump: bestJump, onAppear: {
                                // Check if this is a new personal best
                                if bestJump.barHeightCm > personalBestHeight && personalBestHeight > 0 {
                                    // Trigger confetti and haptic feedback
                                    showingPersonalBest = true
                                    generateHapticFeedback()
                                }
                                personalBestHeight = bestJump.barHeightCm
                            })
                            .padding(.horizontal)
                        }

                    if sessions.isEmpty {
                        // Empty state
                        VStack(spacing: 20) {
                            PoleVaultIcon(size: CGSize(width: 80, height: 80), foregroundColor: AppTheme.accentColor)

                            Text("No Data Yet")
                                .font(.title2)
                                .fontWeight(.bold)

                            Text("Add some sessions and jumps to see your analytics")
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.center)
                                .padding(.horizontal)
                        }
                        .padding(.top, 40)
                    } else {
                        // Stats cards
                        LazyVGrid(columns: [GridItem(.flexible()), GridItem(.flexible())], spacing: 16) {
                            StatCard(title: "Sessions", value: "\(filteredSessions.count)", icon: "list.clipboard")

                            StatCard(title: "Jumps", value: "\(filteredJumps.count)", icon: "figure.gymnastics", useCustomIcon: true)

                            StatCard(title: "Success Rate", value: "\(successRate)%", icon: "checkmark.circle")

                            StatCard(title: "Best Height", value: bestHeightString, icon: "arrow.up")
                        }
                        .padding(.horizontal)

                        // Height progression chart
                        VStack(alignment: .leading, spacing: 16) {
                            Text("Height Progression")
                                .font(.title3)
                                .fontWeight(.bold)
                                .padding(.horizontal)
                                .padding(.top, 16)

                            if heightChartData.isEmpty {
                                Text("Not enough data to display chart")
                                    .foregroundColor(.secondary)
                                    .frame(maxWidth: .infinity, alignment: .center)
                                    .padding()
                            } else {
                                HeightProgressionChart(data: heightChartData)
                                    .frame(height: 220)
                                    .padding(.horizontal)
                            }
                        }
                        .padding(.top, 16)
                        .background(Color(.systemBackground))
                        .cornerRadius(12)
                        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
                        .padding(.horizontal)

                        // Jump success rate chart
                        VStack(alignment: .leading, spacing: 16) {
                            Text("Jump Success Rate")
                                .font(.title3)
                                .fontWeight(.bold)
                                .padding(.horizontal)
                                .padding(.top, 16)

                            if jumpDistribution.isEmpty {
                                Text("Not enough data to display chart")
                                    .foregroundColor(.secondary)
                                    .frame(maxWidth: .infinity, alignment: .center)
                                    .padding()
                            } else {
                                JumpSuccessRateChart(data: jumpDistribution)
                                    .frame(height: 220)
                                    .padding(.horizontal)
                            }
                        }
                        .padding(.top, 16)
                        .background(Color(.systemBackground))
                        .cornerRadius(12)
                        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
                        .padding(.horizontal)

                        // Recent sessions
                        VStack(alignment: .leading, spacing: 16) {
                            Text("Recent Sessions")
                                .font(.title3)
                                .fontWeight(.bold)
                                .padding(.horizontal)
                                .padding(.top, 16)

                            ScrollView(.horizontal, showsIndicators: false) {
                                HStack(spacing: 16) {
                                    ForEach(recentSessions) { session in
                                        RecentSessionCard(session: session)
                                    }
                                }
                                .padding(.horizontal)
                            }
                        }
                        .padding(.top, 16)
                        .background(Color(.systemBackground))
                        .cornerRadius(12)
                        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
                        .padding(.horizontal)

                        // Jump results scatter chart
                        VStack(alignment: .leading, spacing: 16) {
                            Text("Jump Results Over Time")
                                .font(.title3)
                                .fontWeight(.bold)
                                .padding(.horizontal)
                                .padding(.top, 16)

                            if jumpScatterData.isEmpty {
                                Text("Not enough data to display chart")
                                    .foregroundColor(.secondary)
                                    .frame(maxWidth: .infinity, alignment: .center)
                                    .padding()
                            } else {
                                JumpResultsScatterChart(data: jumpScatterData)
                                    .frame(height: 220)
                                    .padding(.horizontal)
                            }
                        }
                        .padding(.top, 16)
                        .background(Color(.systemBackground))
                        .cornerRadius(12)
                        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
                        .padding(.horizontal)
                    }
                }
                .padding(.vertical)
            }
            .navigationTitle("Dashboard")
            .id(refreshID) // Force refresh when refreshID changes
            .onAppear {
                setupNotificationObservers()
            }
                }

                // Confetti overlay
                ConfettiView(isShowing: $showingPersonalBest)
            }
        }
    }

extension DashboardView {
    private func generateHapticFeedback() {
        let generator = UINotificationFeedbackGenerator()
        generator.notificationOccurred(.success)
    }

    // MARK: - Computed Properties

    private var filteredSessions: [Session] {
        return Array(sessions).filter { session in
            // Convert TimeInterval to Date
            let date = Date(timeIntervalSinceReferenceDate: session.date)
            return isDateInRange(date)
        }
    }

    // Data structure for height progression chart
    struct HeightDataPoint: Identifiable {
        let id = UUID()
        let date: Date
        let height: Double
        let sessionTitle: String
    }

    private var heightChartData: [HeightDataPoint] {
        let sessionsWithHeight = filteredSessions.filter { $0.bestHeightCm > 0 }
        let sorted = sessionsWithHeight.sorted { s1, s2 in
            // Convert TimeInterval to Date
            let date1 = Date(timeIntervalSinceReferenceDate: s1.date)
            let date2 = Date(timeIntervalSinceReferenceDate: s2.date)
            return date1 < date2
        }

        return sorted.map { session in
            // Convert TimeInterval to Date
            let date = Date(timeIntervalSinceReferenceDate: session.date)
            return HeightDataPoint(
                date: date,
                height: session.bestHeightCm,
                sessionTitle: session.title ?? "Untitled"
            )
        }
    }

    // Data for jump success rate chart
    private var jumpDistribution: [(label: String, count: Int, percentage: Double)] {
        let makes = filteredJumps.filter { $0.result == "make" }.count
        let misses = filteredJumps.filter { $0.result == "miss" }.count
        let passes = filteredJumps.filter { $0.result == "pass" }.count

        let total = makes + misses + passes
        guard total > 0 else { return [] }

        let makePercentage = Double(makes) / Double(total) * 100
        let missPercentage = Double(misses) / Double(total) * 100
        let passPercentage = Double(passes) / Double(total) * 100

        return [
            ("Make", makes, makePercentage),
            ("Miss", misses, missPercentage),
            ("Pass", passes, passPercentage)
        ]
    }

    // Data for jump results scatter chart
    private var jumpScatterData: [JumpResultsScatterChart.JumpDataPoint] {
        let jumps = filteredJumps

        return jumps.compactMap { jump in
            guard let session = jump.session else { return nil }

            // Convert TimeInterval to Date
            let date = Date(timeIntervalSinceReferenceDate: session.date)

            return JumpResultsScatterChart.JumpDataPoint(
                date: date,
                height: jump.barHeightCm,
                result: jump.result ?? "miss",
                sessionTitle: session.title ?? "Untitled",
                comment: jump.comment
            )
        }.sorted { $0.date < $1.date }
    }

    private var filteredJumps: [Jump] {
        let jumps = filteredSessions.flatMap { session in
            session.jumps?.allObjects as? [Jump] ?? []
        }
        return jumps
    }

    private var recentSessions: [Session] {
        let sorted = filteredSessions.sorted { s1, s2 in
            // Convert TimeInterval to Date
            let date1 = Date(timeIntervalSinceReferenceDate: s1.date)
            let date2 = Date(timeIntervalSinceReferenceDate: s2.date)
            return date1 > date2
        }
        return Array(sorted.prefix(5))
    }

    private var successRate: Int {
        let totalJumps = filteredJumps.filter { $0.result == "make" || $0.result == "miss" }.count
        if totalJumps == 0 { return 0 }

        let makes = filteredJumps.filter { $0.result == "make" }.count
        return Int((Double(makes) / Double(totalJumps)) * 100)
    }

    private var bestHeightString: String {
        let bestJump = filteredJumps.filter { $0.result == "make" }
            .max(by: { $0.barHeightCm < $1.barHeightCm })

        if let bestJump = bestJump {
            if AppTheme.useMetricSystem {
                return HeightConverter.cmToMetersString(bestJump.barHeightCm)
            } else {
                return HeightConverter.cmToFeetInchesString(bestJump.barHeightCm)
            }
        } else {
            return "N/A"
        }
    }

    // MARK: - Helper Methods

    private func isDateInRange(_ date: Date) -> Bool {
        let calendar = Calendar.current
        let now = Date()

        switch timeRange {
        case .week:
            let startOfWeek = calendar.date(from: calendar.dateComponents([.yearForWeekOfYear, .weekOfYear], from: now))!
            return date >= startOfWeek
        case .month:
            let components = calendar.dateComponents([.year, .month], from: now)
            let startOfMonth = calendar.date(from: components)!
            return date >= startOfMonth
        case .year:
            let components = calendar.dateComponents([.year], from: now)
            let startOfYear = calendar.date(from: components)!
            return date >= startOfYear
        case .all:
            return true
        }
    }
}

// MARK: - Supporting Views

struct StatCard: View {
    let title: String
    let value: String
    let icon: String
    var useCustomIcon: Bool = false

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                if useCustomIcon {
                    PoleVaultIcon(size: CGSize(width: 20, height: 20), foregroundColor: AppTheme.accentColor)
                } else {
                    Image(systemName: icon)
                        .foregroundColor(AppTheme.accentColor)
                }
                Text(title)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }

            Text(value)
                .font(.title)
                .fontWeight(.bold)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
    }
}

struct PersonalBestCard: View {
    @ObservedObject var jump: Jump
    var onAppear: () -> Void

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "trophy.fill")
                    .font(.title2)
                    .foregroundColor(.yellow)

                Text("Personal Best")
                    .font(.headline)

                Spacer()

                if let session = jump.session {
                    // Convert TimeInterval to Date
                    let date = Date(timeIntervalSinceReferenceDate: session.date)
                    Text(DateFormatters.mediumDate.string(from: date))
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            VStack(alignment: .leading, spacing: 4) {
                // Primary height display
                if AppTheme.useMetricSystem {
                    Text(HeightConverter.cmToMetersString(jump.barHeightCm))
                        .font(.system(size: 36, weight: .bold, design: .rounded))
                        .foregroundColor(AppTheme.accentColor)
                } else {
                    Text(HeightConverter.cmToFeetInchesString(jump.barHeightCm))
                        .font(.system(size: 36, weight: .bold, design: .rounded))
                        .foregroundColor(AppTheme.accentColor)
                }

                // Secondary height display
                if AppTheme.useMetricSystem {
                    Text(HeightConverter.cmToFeetInchesString(jump.barHeightCm))
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                } else {
                    Text(HeightConverter.cmToMetersString(jump.barHeightCm))
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }

                Spacer()

                if let session = jump.session {
                    Text(session.title ?? "Untitled Session")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                }
            }

            if let session = jump.session, let location = session.location, !location.isEmpty {
                HStack {
                    Image(systemName: "mappin.and.ellipse")
                        .font(.caption)
                    Text(location)
                        .font(.caption)
                }
                .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .strokeBorder(LinearGradient(
                    colors: [.yellow, .orange],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                ), lineWidth: 2)
        )
        .onAppear(perform: onAppear)
    }
}

struct RecentSessionCard: View {
    @ObservedObject var session: Session

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(session.title ?? "Untitled Session")
                    .font(.headline)
                    .lineLimit(1)
                Spacer()
            }

            // Convert TimeInterval to Date
            let date = Date(timeIntervalSinceReferenceDate: session.date)
            Text(DateFormatters.mediumDate.string(from: date))
                .font(.caption)
                .foregroundColor(.secondary)

            Divider()

            HStack {
                if session.bestHeightCm > 0 {
                    HStack(spacing: 4) {
                        Image(systemName: "arrow.up")
                            .font(.caption)
                        if AppTheme.useMetricSystem {
                            Text(HeightConverter.cmToMetersString(session.bestHeightCm))
                                .font(.caption)
                        } else {
                            Text(HeightConverter.cmToFeetInchesString(session.bestHeightCm))
                                .font(.caption)
                        }
                    }
                    .foregroundColor(AppTheme.accentColor)
                }

                Spacer()

                if let type = session.type {
                    Text(type)
                        .font(.caption)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(type == "Practice" ? Color.gray.opacity(0.2) : Color.orange.opacity(0.2))
                        .cornerRadius(4)
                }
            }
        }
        .padding()
        .frame(width: 200)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
    }
}

// MARK: - Chart Components

struct HeightProgressionChart: View {
    let data: [DashboardView.HeightDataPoint]
    @State private var selectedDataPoint: DashboardView.HeightDataPoint?

    var body: some View {
        Chart {
            ForEach(data) { dataPoint in
                LineMark(
                    x: .value("Date", dataPoint.date),
                    y: .value("Height", dataPoint.height)
                )
                .foregroundStyle(AppTheme.accentColor)
                .interpolationMethod(.catmullRom)

                PointMark(
                    x: .value("Date", dataPoint.date),
                    y: .value("Height", dataPoint.height)
                )
                .foregroundStyle(AppTheme.accentColor)
                .symbolSize(selectedDataPoint?.id == dataPoint.id ? 150 : 50)
            }

            if let selected = selectedDataPoint {
                RuleMark(
                    x: .value("Selected Date", selected.date)
                )
                .foregroundStyle(Color.gray.opacity(0.3))
                .lineStyle(StrokeStyle(lineWidth: 1, dash: [5, 5]))
                .annotation(position: .top) {
                    VStack(alignment: .leading, spacing: 6) {
                        Text(selected.sessionTitle)
                            .font(.caption)
                            .foregroundColor(.secondary)

                        if AppTheme.useMetricSystem {
                            Text(HeightConverter.cmToMetersString(selected.height))
                                .font(.headline)
                                .foregroundColor(AppTheme.accentColor)
                        } else {
                            Text(HeightConverter.cmToFeetInchesString(selected.height))
                                .font(.headline)
                                .foregroundColor(AppTheme.accentColor)
                        }
                    }
                    .padding(8)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color(.systemBackground))
                            .shadow(color: Color.black.opacity(0.1), radius: 3)
                    )
                }
            }
        }
        .chartXAxis {
            AxisMarks(values: .automatic) { value in
                AxisGridLine()
                AxisValueLabel {
                    if let date = value.as(Date.self) {
                        Text(DateFormatters.shortDate.string(from: date))
                            .font(.caption)
                    }
                }
            }
        }
        .chartYAxis {
            AxisMarks(position: .leading) { value in
                AxisGridLine()
                AxisValueLabel {
                    if let height = value.as(Double.self) {
                        if AppTheme.useMetricSystem {
                            Text(HeightConverter.cmToMetersString(height))
                                .font(.caption)
                        } else {
                            Text(HeightConverter.cmToFeetInchesString(height))
                                .font(.caption)
                        }
                    }
                }
            }
        }
        .chartOverlay { proxy in
            GeometryReader { geometry in
                Rectangle()
                    .fill(Color.clear)
                    .contentShape(Rectangle())
                    .gesture(
                        DragGesture()
                            .onChanged { value in
                                guard let plotFrame = proxy.plotFrame else {
                                    selectedDataPoint = nil
                                    return
                                }
                                let x = value.location.x - geometry[plotFrame].origin.x
                                guard x >= 0, x <= geometry[plotFrame].width else {
                                    selectedDataPoint = nil
                                    return
                                }

                                // Find the closest data point
                                let closestPoint = data.min(by: {
                                    let position1 = proxy.position(forX: $0.date)
                                    let position2 = proxy.position(forX: $1.date)

                                    guard let pos1 = position1, let pos2 = position2 else {
                                        return false
                                    }

                                    return abs(pos1 - x) < abs(pos2 - x)
                                })
                                selectedDataPoint = closestPoint
                            }
                            .onEnded { _ in
                                // Keep the selected point visible
                            }
                    )
            }
        }
        .frame(height: 220)
        .padding(.vertical)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
    }
}

struct JumpSuccessRateChart: View {
    let data: [(label: String, count: Int, percentage: Double)]

    var body: some View {
        Chart {
            ForEach(data, id: \.label) { item in
                BarMark(
                    x: .value("Type", item.label),
                    y: .value("Count", item.count)
                )
                .foregroundStyle(by: .value("Type", item.label))
                .annotation(position: .top) {
                    Text("\(item.count)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
        .chartForegroundStyleScale([
            "Make": Color.green,
            "Miss": Color.red,
            "Pass": Color.gray
        ])
        .frame(height: 220)
        .padding(.vertical)
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
    }
}

#Preview {
    DashboardView()
        .environment(\.managedObjectContext, PersistenceController(inMemory: true).container.viewContext)
}
