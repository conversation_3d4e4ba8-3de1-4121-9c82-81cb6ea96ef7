import SwiftUI
import CoreData
import UniformTypeIdentifiers

struct ExportAllView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.managedObjectContext) private var viewContext

    @FetchRequest(
        sortDescriptors: [NSSortDescriptor(keyPath: \Athlete.name, ascending: true)],
        animation: .default)
    private var athletes: FetchedResults<Athlete>

    @FetchRequest(
        sortDescriptors: [NSSortDescriptor(keyPath: \Session.date, ascending: false)],
        animation: .default)
    private var sessions: FetchedResults<Session>

    @State private var isExporting = false
    @State private var exportURL: URL?
    @State private var showingAlert = false
    @State private var alertTitle = ""
    @State private var alertMessage = ""
    @State private var exportFormat: ExportFormat = .json

    enum ExportFormat: String, CaseIterable, Identifiable {
        case json = "JSON (.pvl.json)"
        case csv = "CSV (.csv)"

        var id: String { self.rawValue }

        var fileExtension: String {
            switch self {
            case .json:
                return ExportImportManager.jsonExtension
            case .csv:
                return ExportImportManager.csvExtension
            }
        }

        var utType: UTType {
            switch self {
            case .json:
                return ExportImportManager.jsonUTType
            case .csv:
                return ExportImportManager.csvUTType
            }
        }
    }

    var body: some View {
        NavigationStack {
            Form {
                Section(header: Text("Data Summary")) {
                    HStack {
                        Text("Athletes")
                        Spacer()
                        Text("\(athletes.count)")
                            .foregroundColor(.secondary)
                    }

                    HStack {
                        Text("Sessions")
                        Spacer()
                        Text("\(sessions.count)")
                            .foregroundColor(.secondary)
                    }

                    HStack {
                        Text("Total Jumps")
                        Spacer()
                        Text("\(totalJumps)")
                            .foregroundColor(.secondary)
                    }
                }

                Section(header: Text("Export Format")) {
                    Picker("Format", selection: $exportFormat) {
                        ForEach(ExportFormat.allCases) { format in
                            Text(format.rawValue).tag(format)
                        }
                    }
                    .pickerStyle(SegmentedPickerStyle())

                    if exportFormat == .json {
                        HStack {
                            Image(systemName: "doc.text")
                                .foregroundColor(.blue)
                            Text("PoleVaultLogPro-Export.pvl.json")
                                .font(.caption)
                        }

                        Text("This file contains all your athletes, sessions, and jumps data in a format that can be imported back into the app.")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    } else {
                        HStack {
                            Image(systemName: "doc.text")
                                .foregroundColor(.green)
                            Text("PoleVaultLogPro-Export.csv")
                                .font(.caption)
                        }

                        Text("This CSV file can be opened in Excel or other spreadsheet applications. It cannot be imported back into the app.")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }

                Section {
                    Button(action: exportAll) {
                        HStack {
                            Spacer()
                            if isExporting {
                                ProgressView()
                                    .padding(.trailing, 5)
                            }
                            Text("Export All Data")
                            Spacer()
                        }
                    }
                    .disabled(isExporting || sessions.isEmpty)
                }

                if sessions.isEmpty {
                    Section {
                        VStack(spacing: 20) {
                            Image(AppTheme.poleVaultIconName)
                                .resizable()
                                .aspectRatio(contentMode: .fit)
                                .frame(width: 80, height: 80)
                                .foregroundColor(AppTheme.accentColor)

                            Text("No Data to Export")
                                .font(.title2)
                                .fontWeight(.bold)

                            Text("Create sessions in the Log tab first")
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.center)
                                .padding(.horizontal)
                        }
                        .padding(.top, 20)
                        .frame(maxWidth: .infinity, alignment: .center)
                    }
                }
            }
            .navigationTitle("Export All Data")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
            .fileExporter(
                isPresented: $isExporting,
                document: AllExportDocument(url: exportURL),
                contentType: exportFormat.utType,
                defaultFilename: "PoleVaultLogPro-Export-\(formattedDate).\(exportFormat.fileExtension)"
            ) { result in
                isExporting = false

                switch result {
                case .success(let url):
                    alertTitle = "Success"
                    alertMessage = "All data exported successfully to \(url.lastPathComponent)"
                    showingAlert = true
                case .failure(let error):
                    alertTitle = "Error"
                    alertMessage = "Failed to export data: \(error.localizedDescription)"
                    showingAlert = true
                }
            }
            .alert(alertTitle, isPresented: $showingAlert) {
                Button("OK") {
                    if alertTitle == "Success" {
                        dismiss()
                    }
                }
            } message: {
                Text(alertMessage)
            }
        }
    }

    private var totalJumps: Int {
        var count = 0
        for session in sessions {
            count += session.jumps?.count ?? 0
        }
        return count
    }

    private var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter.string(from: Date())
    }

    private func exportAll() {
        isExporting = true

        do {
            switch exportFormat {
            case .json:
                exportURL = try ExportImportManager.shared.exportAllToJSON(
                    athletes: Array(athletes),
                    sessions: Array(sessions)
                )
            case .csv:
                exportURL = try ExportImportManager.shared.exportAllToCSV(
                    sessions: Array(sessions)
                )
            }
        } catch {
            isExporting = false
            alertTitle = "Error"
            alertMessage = "Failed to prepare export: \(error.localizedDescription)"
            showingAlert = true
        }
    }
}

/// A FileDocument for exporting all data
struct AllExportDocument: FileDocument {
    static var readableContentTypes: [UTType] { [ExportImportManager.jsonUTType, ExportImportManager.csvUTType] }

    let url: URL?

    init(url: URL?) {
        self.url = url
    }

    init(configuration: ReadConfiguration) throws {
        url = nil
    }

    func fileWrapper(configuration: WriteConfiguration) throws -> FileWrapper {
        guard let url = url else {
            throw NSError(domain: "ExportAllView", code: 1, userInfo: [NSLocalizedDescriptionKey: "No export URL provided"])
        }

        return try FileWrapper(url: url)
    }
}
