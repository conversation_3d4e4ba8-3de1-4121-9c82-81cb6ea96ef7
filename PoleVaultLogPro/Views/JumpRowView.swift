import SwiftUI
import PhotosUI
import CoreData

struct JumpRowView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @ObservedObject var jump: Jump

    // State for video playback
    @State private var showingVideoPlayer = false

    // State to force refresh when media files change
    @State private var refreshID = UUID()

    // Result colors
    private let makeColor = Color.green
    private let missColor = Color.red
    private let passColor = Color.gray

    var body: some View {
        VStack(spacing: 0) {
            // Check for media using Core Data
            let mediaItems = jump.getMediaItems()
            let hasMedia = !mediaItems.isEmpty
            let isVideo = mediaItems.contains(where: { $0.type == .video })

            // Media playback button at top center (only if media exists)
            if hasMedia {
                HStack {
                    Spacer()
                    // Use Core Data media items
                    NavigationLink(destination: MediaBrowserView(jump: jump)) {
                        Image(systemName: isVideo ? "play.circle.fill" : "photo.circle.fill")
                            .font(.system(size: 24))
                            .foregroundColor(.blue)
                    }
                    .buttonStyle(BorderlessButtonStyle())
                    Spacer()
                }
                .padding(.bottom, -20) // Negative margin to overlap with content below
                .zIndex(1) // Ensure it's above other content
            }

            // Main row content
            HStack {
                // Order number
                Text("\(jump.order)")
                    .font(.headline)
                    .foregroundColor(.secondary)
                    .frame(width: 30)

                // Height
                VStack(alignment: .leading) {
                    Text(heightString)
                        .font(.headline)

                    HStack(spacing: 4) {
                        // Show bar/bungee indicator for practice sessions
                        if let session = jump.session, session.type?.lowercased() == "practice" {
                            Image(systemName: jump.useBar ? "minus" : "water.waves")
                                .font(.caption)
                                .foregroundColor(jump.useBar ? .primary : .blue)
                        }

                        if let comment = jump.comment, !comment.isEmpty {
                            Text(comment)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }

                Spacer()

                // Media indicator (now on the left of bar/bungee)
                if hasMedia {
                    Image(systemName: isVideo ? "video.fill" : "photo.fill")
                        .font(.caption)
                        .foregroundColor(.blue)
                        .padding(.trailing, 4)
                }

                // Result indicator (without media functionality)
                Button(action: toggleResult) {
                    ZStack {
                        Circle()
                            .fill(resultColor)
                            .frame(width: 36, height: 36)

                        // Result icon
                        Image(systemName: resultIcon)
                            .foregroundColor(.white)
                    }
                }
            }
        }
        .padding(.vertical, 4)
        .contentShape(Rectangle())
        .contextMenu {
            Button(action: {
                // Edit jump action
            }) {
                Label("Edit Jump", systemImage: "pencil")
            }

            Button(role: .destructive, action: {
                // Delete jump action
                deleteJump()
            }) {
                Label("Delete Jump", systemImage: "trash")
            }
        }
        .id(refreshID) // Force refresh when refreshID changes
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("MediaFilesChanged"))) { _ in
            // Refresh the view when media files are changed
            print("Refreshing JumpRowView due to media files change")
            refreshID = UUID() // Force a refresh
        }
    }

    private var heightString: String {
        // Format the height based on the stored values
        let cmString = String(format: "%.0f cm", jump.barHeightCm)
        let inString = HeightConverter.cmToFeetInchesString(jump.barHeightCm)
        return "\(cmString) (\(inString))"
    }

    private var resultColor: Color {
        switch jump.result {
        case "make":
            return makeColor
        case "miss":
            return missColor
        case "pass":
            return passColor
        default:
            return .gray
        }
    }

    private var resultIcon: String {
        switch jump.result {
        case "make":
            return "checkmark"
        case "miss":
            return "xmark"
        case "pass":
            return "arrow.right"
        default:
            return "questionmark"
        }
    }

    private func toggleResult() {
        withAnimation {
            // Save the previous result
            let previousResult = jump.result

            // Save the current height values to ensure they don't get lost
            let currentHeightCm = jump.barHeightCm
            let currentHeightIn = jump.barHeightIn

            // Update the result
            switch jump.result {
            case "make":
                jump.result = "miss"
                jump.resultCode = "X"
            case "miss":
                jump.result = "pass"
                jump.resultCode = "P"
            case "pass":
                jump.result = "make"
                jump.resultCode = "O"
            default:
                jump.result = "make"
                jump.resultCode = "O"
            }

            // Ensure the height values are preserved
            jump.barHeightCm = currentHeightCm
            jump.barHeightIn = currentHeightIn

            // Check if the result changed
            _ = previousResult != jump.result

            // Check if this is a change from miss to make or pass
            let wasChangedFromMiss = previousResult == "miss" && (jump.result == "make" || jump.result == "pass")

            do {
                // If any jump is changed to a pass or make, delete any subsequent jumps at this height
                // Only for meets, not for practices
                if let session = jump.session, (jump.result == "pass" || jump.result == "make") {
                    // Only enforce this rule for meets
                    if session.type?.lowercased() == "meet" {
                        let allJumps = session.jumps?.allObjects as? [Jump] ?? []
                        let jumpsToDelete = allJumps.filter { otherJump in
                            abs(otherJump.barHeightCm - jump.barHeightCm) < 0.1 &&
                            otherJump.attemptIndex > jump.attemptIndex &&
                            otherJump.id != jump.id // Make sure we don't delete the current jump
                        }

                        for jumpToDelete in jumpsToDelete {
                            viewContext.delete(jumpToDelete)
                        }
                    }
                }

                // Save the changes
                try viewContext.save()

                // Update session best height if needed
                if let session = jump.session {
                    updateSessionBestHeight(session: session)

                    // Update the view model
                    let viewModel = SessionViewModel(session: session)

                    // If we're changing from a miss to a pass or make, explicitly reset the out status
                    if wasChangedFromMiss {
                        // First reset the out status
                        viewModel.resetOutStatus()
                    }

                    // Then recalculate the out status based on all jumps
                    viewModel.recalculateOutStatus()

                    // Force UI update to ensure the display is updated
                    viewModel.objectWillChange.send()

                    // Notify any observers that the session has changed
                    NotificationCenter.default.post(name: NSNotification.Name("SessionDataChanged"), object: session)
                }

                // Check for any jumps with zero height and delete them
                cleanupZeroHeightJumps()
            } catch {
                let nsError = error as NSError
                print("Error toggling jump result: \(nsError), \(nsError.userInfo)")
            }
        }
    }

    /// Cleans up any jumps with zero height that might have been created accidentally
    private func cleanupZeroHeightJumps() {
        guard let session = jump.session else { return }

        // Find any jumps with zero or very small height
        let fetchRequest = Jump.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "session == %@ AND barHeightCm < 1.0", session)

        do {
            let zeroHeightJumps = try viewContext.fetch(fetchRequest)
            print("Found \(zeroHeightJumps.count) jumps with zero height")

            // Delete these jumps
            for jumpToDelete in zeroHeightJumps {
                viewContext.delete(jumpToDelete)
            }

            // Save the changes
            if !zeroHeightJumps.isEmpty {
                try viewContext.save()
                print("Deleted \(zeroHeightJumps.count) jumps with zero height")

                // Notify any observers that the session has changed
                NotificationCenter.default.post(name: NSNotification.Name("SessionDataChanged"), object: session)
            }
        } catch {
            print("Error cleaning up zero height jumps: \(error)")
        }
    }

    private func deleteJump() {
        withAnimation {
            let session = jump.session
            viewContext.delete(jump)

            do {
                try viewContext.save()

                // Update session best height if needed
                if let session = session {
                    updateSessionBestHeight(session: session)

                    // Update the view model
                    let viewModel = SessionViewModel(session: session)

                    // Recalculate the out status based on all jumps
                    viewModel.recalculateOutStatus()

                    // Force UI update to ensure the display is updated
                    viewModel.objectWillChange.send()

                    // Notify any observers that the session has changed
                    NotificationCenter.default.post(name: NSNotification.Name("SessionDataChanged"), object: session)

                    // Clean up any jumps with zero height
                    cleanupZeroHeightJumps()
                }
            } catch {
                let nsError = error as NSError
                print("Error deleting jump: \(nsError), \(nsError.userInfo)")
            }
        }
    }

    private func updateSessionBestHeight(session: Session) {
        // Fetch all jumps for this session
        let fetchRequest = Jump.fetchRequest()
        fetchRequest.predicate = NSPredicate(format: "session == %@ AND result == %@", session, "make")

        do {
            let jumps = try viewContext.fetch(fetchRequest)
            let bestJump = jumps.max(by: { $0.barHeightCm < $1.barHeightCm })

            if let bestJump = bestJump {
                session.bestHeightCm = bestJump.barHeightCm
                session.bestHeightIn = bestJump.barHeightIn
            } else {
                session.bestHeightCm = 0
                session.bestHeightIn = 0
            }

            try viewContext.save()
        } catch {
            let nsError = error as NSError
            print("Error updating session best height: \(nsError), \(nsError.userInfo)")
        }
    }
}

// Preview is handled in the app's preview environment
