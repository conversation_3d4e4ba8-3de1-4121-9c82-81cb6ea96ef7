import SwiftUI
import CloudKit
import Foundation
import CoreData

/// Represents the current status of CloudKit synchronization
/// We need to implement Equatable manually because the enum has an associated value
enum CloudKitSyncStatus: Equatable {
    case available
    case unavailable
    case unknown
    case error(String)
    case badContainer

    static func == (lhs: CloudKitSyncStatus, rhs: CloudKitSyncStatus) -> Bool {
        switch (lhs, rhs) {
        case (.available, .available):
            return true
        case (.unavailable, .unavailable):
            return true
        case (.unknown, .unknown):
            return true
        case (.badContainer, .badContainer):
            return true
        case (.error(let lhsMessage), .error(let rhsMessage)):
            return lhsMessage == rhsMessage
        default:
            return false
        }
    }

    var description: String {
        switch self {
        case .available:
            return "iCloud sync is active"
        case .unavailable:
            return "iCloud sync is not available"
        case .unknown:
            return "iCloud sync status unknown"
        case .error(let message):
            return "iCloud error: \(message)"
        case .badContainer:
            return "CloudKit container not configured"
        }
    }

    var iconName: String {
        switch self {
        case .available:
            return "checkmark.icloud.fill"
        case .unavailable:
            return "xmark.icloud"
        case .unknown:
            return "icloud.slash"
        case .error:
            return "exclamationmark.icloud.fill"
        case .badContainer:
            return "gearshape.fill"
        }
    }

    var iconColor: Color {
        switch self {
        case .available:
            return .green
        case .unavailable:
            return .gray
        case .unknown:
            return .gray
        case .error:
            return .red
        case .badContainer:
            return .orange
        }
    }
}

/// A view that displays the current CloudKit sync status
struct CloudKitStatusView: View {
    @Environment(\.managedObjectContext) private var viewContext

    @State private var syncStatus: CloudKitSyncStatus = .unknown
    @State private var isChecking = false
    @State private var lastSyncTime: Date? = nil

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("iCloud Sync Status")
                    .font(.headline)

                Spacer()

                if isChecking {
                    ProgressView()
                        .scaleEffect(0.7)
                }

                Button(action: { checkCloudKitStatus() }) {
                    Image(systemName: "arrow.clockwise")
                        .font(.subheadline)
                }
                .disabled(isChecking)
            }

            HStack {
                Image(systemName: syncStatus.iconName)
                    .foregroundColor(syncStatus.iconColor)

                Text(syncStatus.description)
                    .font(.subheadline)

                Spacer()
            }

            if case .badContainer = syncStatus {
                Text("CloudKit container needs to be configured in your Apple Developer account.")
                    .font(.caption)
                    .foregroundColor(.orange)
                    .padding(.top, 2)

                Text("This is required for syncing between devices.")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.top, 1)

                Link("Learn how to set up CloudKit", destination: URL(string: "https://developer.apple.com/documentation/cloudkit/enabling_cloudkit_in_your_app")!)
                    .font(.caption)
                    .padding(.top, 4)
            } else if case .error(let message) = syncStatus, !message.isEmpty {
                Text(message)
                    .font(.caption)
                    .foregroundColor(.red)
            }

            // Add information about sync frequency
            Text("Data syncs automatically between your devices. Sync timing varies based on network conditions and system status.")
                .font(.caption)
                .foregroundColor(.secondary)
                .padding(.top, 2)

            if syncStatus == .available {
                Divider()

                VStack(alignment: .leading, spacing: 8) {
                    if let lastSync = lastSyncTime {
                        HStack {
                            Text("Last sync:")
                                .font(.caption)

                            Text(lastSync, style: .relative)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }

                    Text("iCloud sync happens automatically in the background")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            // Diagnostic button removed - diagnostics functionality has been removed
        }
        .padding(.vertical, 8)
        .onAppear {
            checkCloudKitStatus()

            // Set up observer for CloudKit data changes
            NotificationCenter.default.addObserver(
                forName: NSPersistentCloudKitContainer.eventChangedNotification,
                object: nil,
                queue: .main
            ) { notification in
                if let event = notification.userInfo?[NSPersistentCloudKitContainer.eventNotificationUserInfoKey]
                    as? NSPersistentCloudKitContainer.Event {

                    // Update last sync time when CloudKit events occur
                    self.lastSyncTime = Date()
                    print("🔄 CloudKit event detected: \(event.type)")
                }
            }
        }
        .onDisappear {
            // Remove notification observers
            NotificationCenter.default.removeObserver(self)
        }
        // Diagnostic sheet removed - diagnostics functionality has been removed
    }



    /// Checks the current CloudKit status
    private func checkCloudKitStatus() {
        guard !isChecking else { return }

        isChecking = true
        let containerIdentifier = AppConfiguration.cloudKitContainerID

        // Use the same API for all iOS versions
        CKContainer(identifier: containerIdentifier).accountStatus { status, error in
            DispatchQueue.main.async {
                if let error = error {
                    self.syncStatus = .error(error.localizedDescription)
                    print("CloudKit account status error: \(error.localizedDescription)")
                    self.isChecking = false
                } else {
                    switch status {
                    case .available:
                        // Verify container configuration
                        self.verifyCloudKitContainer(containerIdentifier)
                    case .noAccount:
                        self.syncStatus = .unavailable
                        print("CloudKit status: No iCloud account is available")
                        self.isChecking = false
                    case .restricted:
                        self.syncStatus = .unavailable
                        print("CloudKit status: iCloud access is restricted")
                        self.isChecking = false
                    case .couldNotDetermine:
                        self.syncStatus = .unknown
                        print("CloudKit status: Could not determine iCloud account status")
                        self.isChecking = false
                    case .temporarilyUnavailable:
                        self.syncStatus = .unavailable
                        print("CloudKit status: iCloud is temporarily unavailable")

                        // Schedule a retry after a delay
                        DispatchQueue.main.asyncAfter(deadline: .now() + 30) {
                            print("Retrying CloudKit connection after temporary unavailability")
                            self.checkCloudKitStatus()
                        }
                        self.isChecking = false
                    @unknown default:
                        self.syncStatus = .unknown
                        print("CloudKit status: Unknown iCloud account status")
                        self.isChecking = false
                    }
                }
            }
        }
    }

    /// Verifies that the CloudKit container is properly configured
    private func verifyCloudKitContainer(_ containerIdentifier: String) {
        let container = CKContainer(identifier: containerIdentifier)

        // Try to fetch the user record to verify container access
        container.fetchUserRecordID { recordID, error in
            DispatchQueue.main.async {
                if let error = error as? CKError {
                    if error.code == .badContainer {
                        self.syncStatus = .badContainer
                        print("CloudKit container not configured: \(error.localizedDescription)")
                    } else if error.code == .internalError {
                        self.syncStatus = .error("Internal CloudKit error. Please try again later.")
                        print("CloudKit internal error: \(error.localizedDescription)")
                    } else {
                        self.syncStatus = .error(error.localizedDescription)
                        print("CloudKit error: \(error.localizedDescription)")
                    }
                } else if recordID != nil {
                    self.syncStatus = .available
                    print("CloudKit container verified with user record ID: \(recordID!.recordName)")
                } else {
                    self.syncStatus = .unknown
                    print("CloudKit container verification returned no record ID and no error")
                }
                self.isChecking = false
            }
        }
    }
}

#Preview {
    CloudKitStatusView()
}
