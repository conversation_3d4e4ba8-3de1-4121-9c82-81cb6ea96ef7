import SwiftUI
import CoreData

struct NewSessionView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @Environment(\.dismiss) private var dismiss
    @StateObject private var standardsManager = MeetStandardsManager.shared

    // Session properties
    @State private var title = ""
    @State private var sessionType = "Practice"
    @State private var location = ""
    @State private var weather = ""
    @State private var date = Date()
    @State private var selectedHeightStandard = "NFHS"

    // Session types
    private let sessionTypes = ["Practice", "Meet"]

    var body: some View {
        NavigationStack {
            Form {
                Section(header: Text("Session Details")) {
                    TextField("Title", text: $title)
                        .onChange(of: sessionType) { oldValue, newValue in
                            updateDefaultTitle()
                        }

                    Picker("Type", selection: $sessionType) {
                        ForEach(sessionTypes, id: \.self) { type in
                            Text(type).tag(type)
                        }
                    }
                    .pickerStyle(.segmented)

                    DatePicker("Date", selection: $date, displayedComponents: [.date])
                        .onChange(of: date) { oldValue, newValue in
                            updateDefaultTitle()
                        }

                    if sessionType == "Meet" {
                        Picker("Height Standard", selection: $selectedHeightStandard) {
                            ForEach(standardsManager.allStandards, id: \.id) { standard in
                                Text(standard.name).tag(standard.id)
                            }
                        }
                    }
                }

                Section(header: Text("Location")) {
                    TextField("Location (optional)", text: $location)
                    TextField("Weather (optional)", text: $weather)
                }
            }
            .navigationTitle("New Session")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("Cancel") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .confirmationAction) {
                    Button("Create") {
                        createSession()
                    }
                    .disabled(title.isEmpty)
                }
            }
            .onAppear {
                updateDefaultTitle()
            }
        }
    }

    private func updateDefaultTitle() {
        if title.isEmpty || title.hasPrefix("Practice – ") || title.hasPrefix("Meet – ") {
            let formatter = DateFormatter()
            formatter.dateFormat = "MMM dd"
            let dateString = formatter.string(from: date)

            if sessionType == "Practice" {
                title = "Practice – \(dateString)"
            } else {
                title = "Meet – Event Name"
            }
        }
    }

    private func createSession() {
        withAnimation {
            // Create a new Session using our extension method
            let newSession = Session.create(in: viewContext)
            newSession.id = UUID().uuidString
            newSession.title = title
            newSession.type = sessionType
            newSession.date = date.timeIntervalSinceReferenceDate
            newSession.location = location
            newSession.weather = weather
            newSession.notesAthlete = ""
            newSession.notesCoach = ""
            newSession.bestHeightCm = 0
            newSession.bestHeightIn = 0

            // Automatically associate with the first athlete
            let fetchRequest: NSFetchRequest<Athlete> = Athlete.fetchRequest()
            do {
                let athletes = try viewContext.fetch(fetchRequest)
                if let firstAthlete = athletes.first {
                    newSession.athlete = firstAthlete
                    print("Session automatically associated with athlete: \(firstAthlete.name ?? "Unknown")")
                } else {
                    print("No athletes found to associate with the session")
                }
            } catch {
                print("Error fetching athletes: \(error)")
            }

            // Set the height standard based on session type
            if sessionType == "Practice" {
                newSession.heightStandard = "Free-Range"
            } else {
                newSession.heightStandard = selectedHeightStandard
            }

            do {
                try viewContext.save()
                dismiss()
            } catch {
                let nsError = error as NSError
                print("Error creating session: \(nsError), \(nsError.userInfo)")
            }
        }
    }
}

#Preview {
    NewSessionView()
        .environment(\.managedObjectContext, PersistenceController(inMemory: true).container.viewContext)
}
