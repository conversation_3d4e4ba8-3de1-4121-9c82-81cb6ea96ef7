import SwiftUI
import CoreData

/// View for handling multiple athletes in the database
/// Allows the user to select which athlete to keep
struct MultipleAthletesView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @Environment(\.dismiss) private var dismiss
    
    @State private var athletes: [Athlete] = []
    @State private var selectedAthlete: Athlete?
    @State private var isLoading = false
    @State private var showingAlert = false
    @State private var alertTitle = ""
    @State private var alertMessage = ""
    @State private var mergeSuccessful = false
    
    var body: some View {
        NavigationStack {
            VStack {
                // Header explanation
                VStack(alignment: .leading, spacing: 12) {
                    Text("Multiple Athletes Detected")
                        .font(.headline)
                        .foregroundColor(AppTheme.accentColor)
                    
                    Text("Your account has multiple athlete profiles, but this app is designed for a single athlete. Please select which athlete profile you want to keep. All sessions and poles from other athletes will be transferred to the selected athlete.")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .padding()
                .background(Color(.systemBackground))
                
                if isLoading {
                    // Loading indicator
                    VStack {
                        ProgressView()
                            .padding()
                        Text("Processing...")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else if athletes.isEmpty {
                    // No athletes found
                    ContentUnavailableView(
                        "No Athletes Found",
                        systemImage: "person.slash",
                        description: Text("There are no athletes in the database.")
                    )
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else {
                    // List of athletes to choose from
                    List(athletes, id: \.objectID) { athlete in
                        AthleteSelectionRow(
                            athlete: athlete,
                            isSelected: selectedAthlete?.objectID == athlete.objectID,
                            onSelect: {
                                selectedAthlete = athlete
                            }
                        )
                    }
                    .listStyle(.insetGrouped)
                }
                
                // Action buttons
                VStack(spacing: 12) {
                    Button(action: {
                        mergeAthletes()
                    }) {
                        Text("Keep Selected Athlete")
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(selectedAthlete != nil ? AppTheme.accentColor : Color.gray)
                            .foregroundColor(.white)
                            .cornerRadius(10)
                    }
                    .disabled(selectedAthlete == nil)
                    .padding(.horizontal)
                    
                    Button(action: {
                        dismiss()
                    }) {
                        Text("Cancel")
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color(.systemGray5))
                            .foregroundColor(.primary)
                            .cornerRadius(10)
                    }
                    .padding(.horizontal)
                }
                .padding(.bottom)
            }
            .navigationTitle("Select Athlete")
            .navigationBarTitleDisplayMode(.inline)
            .onAppear {
                loadAthletes()
            }
            .alert(alertTitle, isPresented: $showingAlert) {
                if mergeSuccessful {
                    Button("OK") {
                        dismiss()
                    }
                } else {
                    Button("OK", role: .cancel) { }
                }
            } message: {
                Text(alertMessage)
            }
        }
    }
    
    /// Loads all athletes from the database
    private func loadAthletes() {
        isLoading = true
        
        // Get all athletes using our utility
        athletes = AthleteManagerUtility.shared.getAllAthletes(in: viewContext)
        
        // Select the first athlete by default
        if let firstAthlete = athletes.first {
            selectedAthlete = firstAthlete
        }
        
        isLoading = false
    }
    
    /// Merges athletes by keeping the selected one
    private func mergeAthletes() {
        guard let athleteToKeep = selectedAthlete else {
            showAlert(title: "Selection Required", message: "Please select an athlete to keep.")
            return
        }
        
        isLoading = true
        
        // Use our utility to merge athletes
        let success = AthleteManagerUtility.shared.mergeAthletes(keepAthlete: athleteToKeep, in: viewContext)
        
        isLoading = false
        
        if success {
            mergeSuccessful = true
            showAlert(
                title: "Athletes Merged",
                message: "All data has been successfully transferred to \(athleteToKeep.name ?? "the selected athlete")."
            )
        } else {
            showAlert(
                title: "Error",
                message: "There was a problem merging the athletes. Please try again."
            )
        }
    }
    
    /// Shows an alert with the given title and message
    private func showAlert(title: String, message: String) {
        alertTitle = title
        alertMessage = message
        showingAlert = true
    }
}

/// Row for selecting an athlete
struct AthleteSelectionRow: View {
    let athlete: Athlete
    let isSelected: Bool
    let onSelect: () -> Void
    
    var body: some View {
        Button(action: onSelect) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(athlete.name ?? "Unknown")
                        .font(.headline)
                    
                    if let dateOfBirth = athlete.dateOfBirth {
                        Text("Born: \(dateOfBirth, formatter: dateFormatter)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    if let dominantHand = athlete.dominantHand {
                        Text("Dominant Hand: \(dominantHand)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    // Show session count
                    if let sessions = athlete.sessions?.allObjects as? [Session], !sessions.isEmpty {
                        Text("\(sessions.count) session\(sessions.count == 1 ? "" : "s")")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    // Show pole count
                    if let poles = athlete.poles?.allObjects as? [Pole], !poles.isEmpty {
                        Text("\(poles.count) pole\(poles.count == 1 ? "" : "s")")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(AppTheme.accentColor)
                        .font(.title2)
                }
            }
        }
        .contentShape(Rectangle())
    }
    
    private var dateFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        return formatter
    }
}

#Preview {
    MultipleAthletesView()
        .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}
