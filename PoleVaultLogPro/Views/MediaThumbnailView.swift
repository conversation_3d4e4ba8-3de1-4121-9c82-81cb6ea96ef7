//
//  MediaThumbnailView.swift
//  PoleVaultLogPro
//
//  Created by <PERSON> on 4/29/25.
//

import SwiftUI
import Photos
import PhotosUI
import AVKit
import CoreData

/// A view that displays a thumbnail for a media item
struct MediaThumbnailView: View {
    /// The media item to display (either JumpMedia entity or JumpMediaStruct)
    var media: Any? = nil

    /// The size of the thumbnail
    var size: CGSize = CGSize(width: 100, height: 100)

    /// Whether to show the media type icon
    var showTypeIcon: Bool = true

    /// Whether to show a loading indicator while the thumbnail is being generated
    var showLoadingIndicator: Bool = true

    /// The corner radius of the thumbnail
    var cornerRadius: CGFloat = 8

    @State private var thumbnail: UIImage?
    @State private var isVideo: Bool = false
    @State private var isLoading: Bool = true
    @State private var error: Error?

    var body: some View {
        ZStack {
            if isLoading {
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle())
            } else if error != nil {
                VStack(spacing: 4) {
                    Image(systemName: "exclamationmark.triangle")
                        .font(.title)
                        .foregroundColor(.orange)
                    Text("Error loading media")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Button(action: {
                        // Reset error and try again
                        self.error = nil
                        self.loadThumbnail()
                    }) {
                        Image(systemName: "arrow.clockwise")
                            .font(.caption)
                            .foregroundColor(AppTheme.accentColor)
                    }
                    .padding(.top, 4)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(Color.gray.opacity(0.1))
            } else if let thumbnail = thumbnail {
                Image(uiImage: thumbnail)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: size.width, height: size.height)
                    .clipShape(RoundedRectangle(cornerRadius: cornerRadius))
                    .overlay(
                        RoundedRectangle(cornerRadius: cornerRadius)
                            .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                    )
                    .overlay(
                        Group {
                            if isVideo && showTypeIcon {
                                ZStack {
                                    Circle()
                                        .fill(Color.black.opacity(0.6))
                                        .frame(width: 24, height: 24)

                                    Image(systemName: "play.fill")
                                        .font(.system(size: 12))
                                        .foregroundColor(.white)
                                }
                                .padding(4)
                                .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .topTrailing)
                            }
                        }
                    )
            } else {
                ZStack {
                    Color.gray.opacity(0.3)
                    VStack {
                        PoleVaultIcon(size: CGSize(width: 40, height: 40))
                            .opacity(0.7)

                        Text("No preview available")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Button(action: {
                            // Try loading again
                            self.loadThumbnail()
                        }) {
                            Image(systemName: "arrow.clockwise")
                                .font(.caption)
                                .foregroundColor(AppTheme.accentColor)
                        }
                        .padding(.top, 4)
                    }
                }
                .frame(width: size.width, height: size.height)
                .clipShape(RoundedRectangle(cornerRadius: cornerRadius))
                .overlay(
                    RoundedRectangle(cornerRadius: cornerRadius)
                        .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                )
            }
        }
        .frame(width: size.width, height: size.height)
        .onAppear {
            loadThumbnail()
        }
    }

    private func loadThumbnail() {
        isLoading = true

        // Check if we have a media object
        guard let mediaObject = media else {
            isLoading = false
            error = NSError(domain: "MediaThumbnailView", code: 400,
                           userInfo: [NSLocalizedDescriptionKey: "No media provided"])
            return
        }

        // Determine the type of media object
        let isJumpMediaEntity = mediaObject is JumpMedia
        let isJumpMediaStruct = mediaObject is JumpMediaStruct

        if !isJumpMediaEntity && !isJumpMediaStruct {
            isLoading = false
            error = NSError(domain: "MediaThumbnailView", code: 400,
                           userInfo: [NSLocalizedDescriptionKey: "Unsupported media type"])
            return
        }

        // Get the media type and asset identifier based on the object type
        let mediaType: String
        let assetIdentifier: String?

        if isJumpMediaEntity {
            let entity = mediaObject as! JumpMedia
            mediaType = entity.type ?? "photo"
            assetIdentifier = entity.assetIdentifier
            isVideo = mediaType == "video"
        } else {
            let struct_ = mediaObject as! JumpMediaStruct
            mediaType = struct_.type.rawValue
            assetIdentifier = struct_.assetIdentifier
            isVideo = struct_.type == .video
        }

        // We no longer cache thumbnails to disk
        // Instead, we'll always load them directly from the Photos library

        // Generate a thumbnail based on the asset identifier
        Task {
            if let assetIdentifier = assetIdentifier {
                print("🖼️ DEBUG: Loading thumbnail for asset ID: \(assetIdentifier)")
                // First try to find the asset as a shared asset
                var asset = SharedMediaManager.shared.fetchSharedAsset(with: assetIdentifier)

                // If not found as a shared asset, try as a local asset
                if asset == nil {
                    let fetchResult = PHAsset.fetchAssets(withLocalIdentifiers: [assetIdentifier], options: nil)
                    asset = fetchResult.firstObject
                }

                if let asset = asset {
                    print("🖼️ DEBUG: Found PHAsset: \(asset.localIdentifier)")
                    // Request a thumbnail from the Photos library
                    let options = PHImageRequestOptions()
                    options.isNetworkAccessAllowed = true
                    options.deliveryMode = .highQualityFormat
                    options.isSynchronous = false

                    // Use a larger size to ensure good quality
                    let targetSize = CGSize(width: size.width * 2, height: size.height * 2)
                    print("🖼️ DEBUG: Requesting image with size: \(targetSize)")

                    let thumbnail = await withCheckedContinuation { continuation in
                        MediaStorageManager.shared.cachingImageManager.requestImage(
                            for: asset,
                            targetSize: targetSize,
                            contentMode: .aspectFill,
                            options: options
                        ) { image, info in
                            print("🖼️ DEBUG: Image request completed. Image: \(image != nil ? "received" : "nil"), Info: \(String(describing: info))")
                            continuation.resume(returning: image)
                        }
                    }

                    if let thumbnail = thumbnail {
                        print("🖼️ DEBUG: Successfully loaded thumbnail with size: \(thumbnail.size)")
                        // We no longer save thumbnails to disk
                        DispatchQueue.main.async {
                            self.thumbnail = thumbnail
                            self.isLoading = false
                        }
                        return
                    } else {
                        print("🖼️ ERROR: Failed to load thumbnail from PHAsset")
                    }
                } else {
                    print("🖼️ ERROR: Could not find PHAsset with ID: \(assetIdentifier)")
                }
            } else {
                print("🖼️ DEBUG: No asset identifier available")
            }

            // If we couldn't get a thumbnail from PHAsset, create a placeholder
            DispatchQueue.main.async {
                if isVideo {
                    self.generateVideoPlaceholder()
                } else {
                    self.generateImagePlaceholder()
                }
                self.isLoading = false
            }
        }
    }



    private func generateVideoPlaceholder() {
        // Create a simple video placeholder
        let renderer = UIGraphicsImageRenderer(size: CGSize(width: 300, height: 300))
        let image = renderer.image { ctx in
            // Fill background
            UIColor.darkGray.setFill()
            ctx.fill(CGRect(x: 0, y: 0, width: 300, height: 300))

            // Draw play icon
            let playImage = UIImage(systemName: "play.fill")?.withTintColor(.white, renderingMode: .alwaysOriginal)
            if let playImage = playImage {
                let playRect = CGRect(x: 125, y: 125, width: 50, height: 50)
                playImage.draw(in: playRect)
            }
        }

        self.thumbnail = image
    }

    private func generateImagePlaceholder() {
        // Create a simple image placeholder
        let renderer = UIGraphicsImageRenderer(size: CGSize(width: 300, height: 300))
        let image = renderer.image { ctx in
            // Fill background
            UIColor.lightGray.setFill()
            ctx.fill(CGRect(x: 0, y: 0, width: 300, height: 300))

            // Draw image icon
            let photoImage = UIImage(systemName: "photo")?.withTintColor(.white, renderingMode: .alwaysOriginal)
            if let photoImage = photoImage {
                let photoRect = CGRect(x: 125, y: 125, width: 50, height: 50)
                photoImage.draw(in: photoRect)
            }
        }

        self.thumbnail = image
    }
}

#Preview {
    // This is just for preview purposes
    VStack(spacing: 20) {

        // New JumpMediaStruct
        MediaThumbnailView(
            media: JumpMediaStruct(
                type: .video,
                assetIdentifier: nil,

                posterTime: 0.5
            ),
            size: CGSize(width: 200, height: 150)
        )
    }
    .padding()
}
