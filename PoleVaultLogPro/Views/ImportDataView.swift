import SwiftUI
import UniformTypeIdentifiers
import CoreData

// MARK: - Sheet Presentation Models
struct OrphanedSessionsSheetItem: Identifiable {
    let id = UUID()
    let orphanedSessions: [SessionExport]
}

struct ImportDataView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.managedObjectContext) private var viewContext
    @Environment(\.presentationMode) private var presentationMode

    // Use a navigation path for more reliable navigation
    @State private var navigationPath = NavigationPath()

    @State private var isImporting = false
    @State private var showingAlert = false
    @State private var alertTitle = ""
    @State private var alertMessage = ""
    @State private var importResult: (athletes: Int, sessions: Int, duplicates: Int)?
    @State private var orphanedSessions: [SessionExport]?
    @State private var orphanedSessionsSheet: OrphanedSessionsSheetItem?
    @State private var importedOrphanedCount = 0

    var body: some View {
        NavigationStack(path: $navigationPath) {
            Form {
                Section("Import Data") {
                    Text("Import data from a .pvl.json file")
                        .font(.subheadline)
                        .foregroundColor(.secondary)

                    Text("This will add new data to your existing data. Existing records with the same ID will be updated.")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .padding(.top, 4)

                    Button(action: { isImporting = true }) {
                        HStack {
                            Spacer()
                            Label("Select File to Import", systemImage: "doc.badge.plus")
                            Spacer()
                        }
                    }
                    .padding(.vertical, 8)


                }

                if let result = importResult {
                    Section("Import Results") {
                        HStack {
                            Text("Athletes Imported")
                            Spacer()
                            Text("\(result.athletes)")
                                .foregroundColor(.secondary)
                        }

                        HStack {
                            Text("Sessions Imported")
                            Spacer()
                            Text("\(result.sessions + importedOrphanedCount)")
                                .foregroundColor(.secondary)
                        }

                        if result.duplicates > 0 {
                            HStack {
                                Text("Sessions Updated (Duplicates)")
                                Spacer()
                                Text("\(result.duplicates)")
                                    .foregroundColor(.secondary)
                            }
                        }

                        if let orphaned = orphanedSessions, !orphaned.isEmpty {
                            Button(action: {
                                orphanedSessionsSheet = OrphanedSessionsSheetItem(orphanedSessions: orphaned)
                            }) {
                                HStack {
                                    Text("Assign Athlete to \(orphaned.count) Orphaned Sessions")
                                        .foregroundColor(.blue)
                                    Spacer()
                                    Image(systemName: "chevron.right")
                                        .foregroundColor(.gray)
                                }
                            }
                        }

                        // Add a Done button at the bottom of the results
                        Button(action: {
                            // Use the dismiss environment value which is more reliable
                            presentationMode.wrappedValue.dismiss()
                        }) {
                            HStack {
                                Spacer()
                                Text("Done")
                                    .fontWeight(.medium)
                                Spacer()
                            }
                            .foregroundColor(.blue)
                        }
                        .buttonStyle(BorderlessButtonStyle()) // Ensure button works in Form
                        .padding(.top, 8)
                    }
                }
            }
            .navigationTitle("Import Data")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
            .fileImporter(
                isPresented: $isImporting,
                allowedContentTypes: [ExportImportManager.jsonUTType],
                allowsMultipleSelection: false
            ) { result in
                isImporting = false

                switch result {
                case .success(let urls):
                    guard let url = urls.first else {
                        alertTitle = "Error"
                        alertMessage = "No file selected"
                        showingAlert = true
                        return
                    }

                    // Start accessing the file
                    guard url.startAccessingSecurityScopedResource() else {
                        alertTitle = "Error"
                        alertMessage = "Failed to access the selected file"
                        showingAlert = true
                        return
                    }

                    // Import the data
                    do {
                        let result = try ExportImportManager.shared.importFromJSON(url: url, context: viewContext)
                        importResult = (athletes: result.athletes, sessions: result.sessions, duplicates: result.duplicateSessions)
                        orphanedSessions = result.orphanedSessions

                        if let orphaned = result.orphanedSessions, !orphaned.isEmpty {
                            alertTitle = "Import Partial"
                            alertMessage = "Successfully imported \(result.athletes) athletes" +
                                (result.sessions > 0 ? ", imported \(result.sessions) new sessions" : "") +
                                (result.duplicateSessions > 0 ? ", updated \(result.duplicateSessions) existing sessions" : "") +
                                ". There are \(orphaned.count) sessions without an athlete. Would you like to assign them now?"
                            showingAlert = true
                        } else if result.sessions == 0 && result.duplicateSessions > 0 {
                            alertTitle = "Import Complete"
                            alertMessage = "No new sessions were imported because they already exist. \(result.duplicateSessions) existing sessions were updated with the latest data."
                            showingAlert = true
                        } else if result.sessions == 0 && result.duplicateSessions == 0 {
                            alertTitle = "Import Complete"
                            alertMessage = "No sessions were imported. This could be because the file contains no sessions or all sessions already exist and are up to date."
                            showingAlert = true
                        } else {
                            alertTitle = "Success"
                            alertMessage = "Successfully imported \(result.athletes) athletes and \(result.sessions) new sessions" +
                                (result.duplicateSessions > 0 ? ", updated \(result.duplicateSessions) existing sessions" : "")
                            showingAlert = true
                        }
                    } catch {
                        alertTitle = "Error"
                        alertMessage = "Failed to import data: \(error.localizedDescription)"
                        showingAlert = true
                    }

                    // Stop accessing the file
                    url.stopAccessingSecurityScopedResource()

                case .failure(let error):
                    alertTitle = "Error"
                    alertMessage = "Failed to select file: \(error.localizedDescription)"
                    showingAlert = true
                }
            }
            .alert(alertTitle, isPresented: $showingAlert) {
                if alertTitle == "Import Partial" {
                    Button("Assign Now") {
                        if let orphaned = orphanedSessions {
                            orphanedSessionsSheet = OrphanedSessionsSheetItem(orphanedSessions: orphaned)
                        }
                    }
                    Button("Later") { }
                } else {
                    Button("OK") { }
                }
            } message: {
                Text(alertMessage)
            }
            .sheet(item: $orphanedSessionsSheet) { item in
                OrphanedSessionsView(orphanedSessions: item.orphanedSessions) { importedCount in
                    importedOrphanedCount = importedCount
                    orphanedSessions = nil
                    orphanedSessionsSheet = nil
                }
            }
        }
    }
}
