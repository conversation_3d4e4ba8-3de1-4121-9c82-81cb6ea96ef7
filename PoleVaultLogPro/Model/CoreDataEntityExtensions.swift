import Foundation
import CoreData

// MARK: - Session Extensions
extension Session {
    /// Creates a new Session with the correct entity description
    /// - Parameter context: The managed object context to use
    /// - Returns: A new Session instance
    static func create(in context: NSManagedObjectContext) -> Session {
        let fetchRequest = CoreDataFetchRequestHelper.createSessionFetchRequest(context: context)
        guard let entityDescription = fetchRequest.entity else {
            fatalError("Could not get entity description for Session")
        }

        return Session(entity: entityDescription, insertInto: context)
    }

    /// Creates a new Session with the correct entity description and all properties set
    /// - Parameters:
    ///   - context: The managed object context to use
    ///   - athlete: Optional athlete for this session
    ///   - type: The type of session (practice, meet, etc.)
    ///   - title: Optional title for the session
    ///   - date: Optional date for the session
    ///   - location: Optional location for the session
    ///   - weather: Optional weather conditions for the session
    ///   - heightStandard: Optional height standard for the session
    /// - Returns: A new Session instance with all properties set
    static func create(
        in context: NSManagedObjectContext,
        athlete: Athlete? = nil,
        type: String,
        title: String? = nil,
        date: Date? = nil,
        location: String? = nil,
        weather: String? = nil,
        heightStandard: String? = nil
    ) -> Session {
        let session = create(in: context)
        session.id = UUID().uuidString
        session.athlete = athlete
        session.type = type
        session.title = title
        session.date = (date ?? Date()).timeIntervalSinceReferenceDate
        session.location = location
        session.weather = weather
        session.heightStandard = heightStandard ?? (type.lowercased() == "practice" ? "Free-Range" : "NFHS")
        session.notesAthlete = ""
        session.notesCoach = ""
        session.bestHeightCm = 0
        session.bestHeightIn = 0

        return session
    }

    /// Fetches sessions with the correct entity description
    /// - Parameters:
    ///   - context: The managed object context to use
    ///   - sortDescriptors: Optional sort descriptors for the fetch request
    ///   - predicate: Optional predicate for filtering results
    /// - Returns: An array of Session objects
    static func fetch(
        in context: NSManagedObjectContext,
        sortDescriptors: [NSSortDescriptor]? = nil,
        predicate: NSPredicate? = nil
    ) -> [Session] {
        let fetchRequest = CoreDataFetchRequestHelper.createSessionFetchRequest(
            context: context,
            sortDescriptors: sortDescriptors,
            predicate: predicate
        )

        do {
            return try context.fetch(fetchRequest)
        } catch {
            print("Error fetching sessions: \(error)")
            return []
        }
    }

    /// Creates a fetch request for Session entities with the correct entity description
    /// - Parameters:
    ///   - context: The managed object context to use
    ///   - sortDescriptors: Optional sort descriptors for the fetch request
    ///   - predicate: Optional predicate for filtering results
    /// - Returns: A configured NSFetchRequest for Session entities
    static func fetchRequest(
        in context: NSManagedObjectContext,
        sortDescriptors: [NSSortDescriptor]? = nil,
        predicate: NSPredicate? = nil
    ) -> NSFetchRequest<Session> {
        return CoreDataFetchRequestHelper.createSessionFetchRequest(
            context: context,
            sortDescriptors: sortDescriptors,
            predicate: predicate
        )
    }
}

// MARK: - Jump Extensions
extension Jump {
    /// Creates a new Jump with the correct entity description
    /// - Parameter context: The managed object context to use
    /// - Returns: A new Jump instance
    static func create(in context: NSManagedObjectContext) -> Jump {
        let fetchRequest = CoreDataFetchRequestHelper.createJumpFetchRequest(context: context)
        guard let entityDescription = fetchRequest.entity else {
            fatalError("Could not get entity description for Jump")
        }

        return Jump(entity: entityDescription, insertInto: context)
    }

    /// Creates a new Jump with the correct entity description and all properties set
    /// - Parameters:
    ///   - context: The managed object context to use
    ///   - session: The session this jump belongs to
    ///   - order: The order of this jump within the session
    ///   - heightCm: The height of the bar in centimeters
    ///   - result: The result of the jump (make, miss, pass)
    ///   - attemptIndex: The attempt index (1, 2, or 3)
    ///   - comment: Optional comment for the jump
    ///   - runStartCm: The run start distance in centimeters
    ///   - handHoldCm: The hand hold height in centimeters
    ///   - takeOffStepCm: The take-off step distance in centimeters
    ///   - standardCm: The standard distance in centimeters
    ///   - useBar: Whether a bar was used (vs. bungee)
    ///   - pole: Optional pole used for the jump
    /// - Returns: A new Jump instance with all properties set
    static func create(
        in context: NSManagedObjectContext,
        session: Session,
        order: Int16,
        heightCm: Double,
        result: String,
        attemptIndex: Int16 = 1,
        comment: String? = nil,
        runStartCm: Double = 0,
        handHoldCm: Double = 0,
        takeOffStepCm: Double = 0,
        standardCm: Double = 0,
        useBar: Bool = true,
        pole: Pole? = nil
    ) -> Jump {
        let jump = create(in: context)
        jump.id = UUID().uuidString
        jump.session = session
        jump.order = order
        jump.barHeightCm = heightCm
        jump.barHeightIn = HeightConverter.cmToInches(heightCm)
        jump.result = result
        jump.resultCode = result == "make" ? "O" : (result == "miss" ? "X" : "P")
        jump.attemptIndex = attemptIndex
        jump.comment = comment
        jump.runStartCm = runStartCm
        jump.handHoldCm = handHoldCm
        jump.takeOffStepCm = takeOffStepCm
        jump.standardCm = standardCm
        jump.useBar = useBar
        jump.pole = pole

        return jump
    }

    /// Creates a new Jump with the correct entity description and minimal properties set
    /// - Parameters:
    ///   - context: The managed object context to use
    ///   - session: The session this jump belongs to
    ///   - order: The order of this jump within the session
    ///   - heightCm: The height of the bar in centimeters
    ///   - result: The result of the jump (make, miss, pass)
    /// - Returns: A new Jump instance with minimal properties set
    static func create(
        in context: NSManagedObjectContext,
        session: Session,
        order: Int16,
        heightCm: Double,
        result: String
    ) -> Jump {
        return create(
            in: context,
            session: session,
            order: order,
            heightCm: heightCm,
            result: result,
            attemptIndex: 1
        )
    }

    /// Fetches jumps with the correct entity description
    /// - Parameters:
    ///   - context: The managed object context to use
    ///   - sortDescriptors: Optional sort descriptors for the fetch request
    ///   - predicate: Optional predicate for filtering results
    /// - Returns: An array of Jump objects
    static func fetch(
        in context: NSManagedObjectContext,
        sortDescriptors: [NSSortDescriptor]? = nil,
        predicate: NSPredicate? = nil
    ) -> [Jump] {
        let fetchRequest = CoreDataFetchRequestHelper.createJumpFetchRequest(
            context: context,
            sortDescriptors: sortDescriptors,
            predicate: predicate
        )

        do {
            return try context.fetch(fetchRequest)
        } catch {
            print("Error fetching jumps: \(error)")
            return []
        }
    }

    /// Creates a fetch request for Jump entities with the correct entity description
    /// - Parameters:
    ///   - context: The managed object context to use
    ///   - sortDescriptors: Optional sort descriptors for the fetch request
    ///   - predicate: Optional predicate for filtering results
    /// - Returns: A configured NSFetchRequest for Jump entities
    static func fetchRequest(
        in context: NSManagedObjectContext,
        sortDescriptors: [NSSortDescriptor]? = nil,
        predicate: NSPredicate? = nil
    ) -> NSFetchRequest<Jump> {
        return CoreDataFetchRequestHelper.createJumpFetchRequest(
            context: context,
            sortDescriptors: sortDescriptors,
            predicate: predicate
        )
    }
}

// MARK: - Athlete Extensions
extension Athlete {
    /// Creates a new Athlete with the correct entity description
    /// - Parameter context: The managed object context to use
    /// - Returns: A new Athlete instance
    static func create(in context: NSManagedObjectContext) -> Athlete {
        let fetchRequest = CoreDataFetchRequestHelper.createAthleteFetchRequest(context: context)
        guard let entityDescription = fetchRequest.entity else {
            fatalError("Could not get entity description for Athlete")
        }

        return Athlete(entity: entityDescription, insertInto: context)
    }

    /// Creates a new Athlete with the correct entity description and all properties set
    /// - Parameters:
    ///   - context: The managed object context to use
    ///   - name: The name of the athlete
    ///   - dominantHand: Optional dominant hand of the athlete
    /// - Returns: A new Athlete instance with all properties set
    static func create(
        in context: NSManagedObjectContext,
        name: String,
        dominantHand: String? = nil
    ) -> Athlete {
        let athlete = create(in: context)
        athlete.id = UUID().uuidString
        athlete.name = name
        athlete.dominantHand = dominantHand

        return athlete
    }

    /// Fetches athletes with the correct entity description
    /// - Parameters:
    ///   - context: The managed object context to use
    ///   - sortDescriptors: Optional sort descriptors for the fetch request
    ///   - predicate: Optional predicate for filtering results
    /// - Returns: An array of Athlete objects
    static func fetch(
        in context: NSManagedObjectContext,
        sortDescriptors: [NSSortDescriptor]? = nil,
        predicate: NSPredicate? = nil
    ) -> [Athlete] {
        let fetchRequest = CoreDataFetchRequestHelper.createAthleteFetchRequest(
            context: context,
            sortDescriptors: sortDescriptors,
            predicate: predicate
        )

        do {
            return try context.fetch(fetchRequest)
        } catch {
            print("Error fetching athletes: \(error)")
            return []
        }
    }

    /// Creates a fetch request for Athlete entities with the correct entity description
    /// - Parameters:
    ///   - context: The managed object context to use
    ///   - sortDescriptors: Optional sort descriptors for the fetch request
    ///   - predicate: Optional predicate for filtering results
    /// - Returns: A configured NSFetchRequest for Athlete entities
    static func fetchRequest(
        in context: NSManagedObjectContext,
        sortDescriptors: [NSSortDescriptor]? = nil,
        predicate: NSPredicate? = nil
    ) -> NSFetchRequest<Athlete> {
        return CoreDataFetchRequestHelper.createAthleteFetchRequest(
            context: context,
            sortDescriptors: sortDescriptors,
            predicate: predicate
        )
    }
}

// MARK: - Pole Extensions
extension Pole {
    /// Creates a new Pole with the correct entity description
    /// - Parameter context: The managed object context to use
    /// - Returns: A new Pole instance
    static func create(in context: NSManagedObjectContext) -> Pole {
        let fetchRequest = CoreDataFetchRequestHelper.createPoleFetchRequest(context: context)
        guard let entityDescription = fetchRequest.entity else {
            fatalError("Could not get entity description for Pole")
        }

        return Pole(entity: entityDescription, insertInto: context)
    }

    /// Fetches poles with the correct entity description
    /// - Parameters:
    ///   - context: The managed object context to use
    ///   - sortDescriptors: Optional sort descriptors for the fetch request
    ///   - predicate: Optional predicate for filtering results
    /// - Returns: An array of Pole objects
    static func fetch(
        in context: NSManagedObjectContext,
        sortDescriptors: [NSSortDescriptor]? = nil,
        predicate: NSPredicate? = nil
    ) -> [Pole] {
        let fetchRequest = CoreDataFetchRequestHelper.createPoleFetchRequest(
            context: context,
            sortDescriptors: sortDescriptors,
            predicate: predicate
        )

        do {
            return try context.fetch(fetchRequest)
        } catch {
            print("Error fetching poles: \(error)")
            return []
        }
    }

    /// Creates a fetch request for Pole entities with the correct entity description
    /// - Parameters:
    ///   - context: The managed object context to use
    ///   - sortDescriptors: Optional sort descriptors for the fetch request
    ///   - predicate: Optional predicate for filtering results
    /// - Returns: A configured NSFetchRequest for Pole entities
    static func fetchRequest(
        in context: NSManagedObjectContext,
        sortDescriptors: [NSSortDescriptor]? = nil,
        predicate: NSPredicate? = nil
    ) -> NSFetchRequest<Pole> {
        return CoreDataFetchRequestHelper.createPoleFetchRequest(
            context: context,
            sortDescriptors: sortDescriptors,
            predicate: predicate
        )
    }
}

// MARK: - JumpMedia Extensions
extension JumpMedia {
    /// Creates a new JumpMedia with the correct entity description
    /// - Parameter context: The managed object context to use
    /// - Returns: A new JumpMedia instance
    static func create(in context: NSManagedObjectContext) -> JumpMedia {
        let fetchRequest = CoreDataFetchRequestHelper.createJumpMediaFetchRequest(context: context)
        guard let entityDescription = fetchRequest.entity else {
            fatalError("Could not get entity description for JumpMedia")
        }

        return JumpMedia(entity: entityDescription, insertInto: context)
    }

    /// Fetches jump media with the correct entity description
    /// - Parameters:
    ///   - context: The managed object context to use
    ///   - sortDescriptors: Optional sort descriptors for the fetch request
    ///   - predicate: Optional predicate for filtering results
    /// - Returns: An array of JumpMedia objects
    static func fetch(
        in context: NSManagedObjectContext,
        sortDescriptors: [NSSortDescriptor]? = nil,
        predicate: NSPredicate? = nil
    ) -> [JumpMedia] {
        let fetchRequest = CoreDataFetchRequestHelper.createJumpMediaFetchRequest(
            context: context,
            sortDescriptors: sortDescriptors,
            predicate: predicate
        )

        do {
            return try context.fetch(fetchRequest)
        } catch {
            print("Error fetching jump media: \(error)")
            return []
        }
    }

    /// Creates a fetch request for JumpMedia entities with the correct entity description
    /// - Parameters:
    ///   - context: The managed object context to use
    ///   - sortDescriptors: Optional sort descriptors for the fetch request
    ///   - predicate: Optional predicate for filtering results
    /// - Returns: A configured NSFetchRequest for JumpMedia entities
    static func fetchRequest(
        in context: NSManagedObjectContext,
        sortDescriptors: [NSSortDescriptor]? = nil,
        predicate: NSPredicate? = nil
    ) -> NSFetchRequest<JumpMedia> {
        return CoreDataFetchRequestHelper.createJumpMediaFetchRequest(
            context: context,
            sortDescriptors: sortDescriptors,
            predicate: predicate
        )
    }
}
