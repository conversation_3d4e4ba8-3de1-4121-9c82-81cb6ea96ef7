<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<model type="com.apple.IDECoreDataModeler.DataModel" documentVersion="1.0" lastSavedToolsVersion="22522" systemVersion="23E214" minimumToolsVersion="Automatic" sourceLanguage="Swift" usedWithCloudKit="YES" userDefinedModelVersionIdentifier="">
    <entity name="Athlete" representedClassName="Athlete" syncable="YES" codeGenerationType="class">
        <attribute name="dateOfBirth" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="dominantHand" optional="YES" attributeType="String"/>
        <attribute name="id" attributeType="String" defaultValueString="" spotlightIndexingEnabled="YES"/>
        <attribute name="name" attributeType="String" defaultValueString="" spotlightIndexingEnabled="YES"/>
        <attribute name="personalBestCm" optional="YES" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <attribute name="personalBestIn" optional="YES" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <relationship name="sessions" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="Session" inverseName="athlete" inverseEntity="Session"/>
    </entity>
    <entity name="GridHeight" representedClassName="GridHeight" syncable="YES" codeGenerationType="class">
        <attribute name="heightCm" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <attribute name="order" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <relationship name="session" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="Session" inverseName="gridHeights" inverseEntity="Session"/>
    </entity>
    <entity name="Jump" representedClassName="Jump" syncable="YES" codeGenerationType="class">
        <attribute name="attemptIndex" optional="YES" attributeType="Integer 16" defaultValueString="1" usesScalarValueType="YES"/>
        <attribute name="barHeightCm" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <attribute name="barHeightIn" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <attribute name="columnIndex" optional="YES" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="comment" optional="YES" attributeType="String"/>
        <attribute name="handHoldCm" optional="YES" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <attribute name="id" attributeType="String" defaultValueString=""/>
        <attribute name="mediaData" optional="YES" attributeType="Binary"/>
        <attribute name="order" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <attribute name="result" attributeType="String" defaultValueString="miss"/>
        <attribute name="resultCode" optional="YES" attributeType="String" defaultValueString="X"/>
        <attribute name="runStartCm" optional="YES" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <attribute name="standardCm" optional="YES" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <attribute name="takeOffStepCm" optional="YES" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <attribute name="useBar" optional="YES" attributeType="Boolean" defaultValueString="YES" usesScalarValueType="YES"/>
        <attribute name="videoLocalId" optional="YES" attributeType="String"/>
        <relationship name="session" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="Session" inverseName="jumps" inverseEntity="Session"/>
    </entity>
    <entity name="Session" representedClassName="Session" syncable="YES" codeGenerationType="class">
        <attribute name="bestHeightCm" optional="YES" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <attribute name="bestHeightIn" optional="YES" attributeType="Double" defaultValueString="0.0" usesScalarValueType="YES"/>
        <attribute name="date" attributeType="Date" defaultDateTimeInterval="0" usesScalarValueType="YES"/>
        <attribute name="heightStandard" optional="YES" attributeType="String" defaultValueString="Free-Range"/>
        <attribute name="id" attributeType="String" defaultValueString=""/>
        <attribute name="location" optional="YES" attributeType="String"/>
        <attribute name="notesAthlete" optional="YES" attributeType="String"/>
        <attribute name="notesCoach" optional="YES" attributeType="String"/>
        <attribute name="title" attributeType="String" defaultValueString=""/>
        <attribute name="type" attributeType="String" defaultValueString="practice"/>
        <attribute name="weather" optional="YES" attributeType="String"/>
        <relationship name="athlete" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="Athlete" inverseName="sessions" inverseEntity="Athlete"/>
        <relationship name="gridHeights" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="GridHeight" inverseName="session" inverseEntity="GridHeight"/>
        <relationship name="jumps" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="Jump" inverseName="session" inverseEntity="Jump"/>
        <relationship name="videoReferences" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="VideoReference" inverseName="session" inverseEntity="VideoReference"/>
    </entity>
    <entity name="VideoReference" representedClassName="VideoReference" syncable="YES" codeGenerationType="class">
        <attribute name="localId" attributeType="String" defaultValueString=""/>
        <attribute name="order" attributeType="Integer 16" defaultValueString="0" usesScalarValueType="YES"/>
        <relationship name="session" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="Session" inverseName="videoReferences" inverseEntity="Session"/>
    </entity>
</model>
