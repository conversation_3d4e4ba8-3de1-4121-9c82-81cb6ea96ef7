import Foundation
import SwiftUI
import Photos
import PhotosUI
import AVFoundation
import CoreData
import UniformTypeIdentifiers
import UIKit

/// Manages the storage and retrieval of media files
class MediaStorageManager: NSObject {
    /// Shared instance
    static let shared = MediaStorageManager()

    // We no longer store media files locally, only thumbnails

    // Thumbnail directory has been removed as we no longer store thumbnails locally

    /// Video cache warning threshold in bytes
    var videoCacheWarningThreshold: Int64 {
        let value = UserDefaults.standard.double(forKey: "videoCacheWarningGB")
        return Int64(value > 0 ? value : 2.0 * 1_000_000_000) // Default to 2 GB if not set
    }

    /// Default poster time for videos in seconds - no longer configurable, fixed at 1.0 seconds
    var defaultPosterTime: Double {
        return 1.0
    }

    // We no longer support the option to always copy picked media
    // Always prioritize using asset identifiers when available

    // Shared PHCachingImageManager for better performance
    lazy var cachingImageManager: PHCachingImageManager = {
        let manager = PHCachingImageManager()
        manager.allowsCachingHighQualityImages = true
        return manager
    }()

    /// Private initializer for singleton
    private override init() {
        // Call super.init() before using self
        super.init()

        // Set default values if not already set
        if UserDefaults.standard.object(forKey: "videoCacheWarningGB") == nil {
            UserDefaults.standard.set(2.0, forKey: "videoCacheWarningGB") // 2 GB default
        }
    }

    /// Process a picked asset: use the SharedMediaManager to add it to a shared album
    func processMediaItem(_ item: PhotosPickerItem) async -> JumpMediaStruct? {
        // First get the local asset identifier
        if let itemId = item.itemIdentifier {
            // Try using the itemIdentifier directly as the asset ID
            let assetId = itemId

            // Verify this is a valid PHAsset
            let fetchResult = PHAsset.fetchAssets(withLocalIdentifiers: [assetId], options: nil)
            if fetchResult.count > 0 {
                // Use the SharedMediaManager to process the item and add it to the shared album
                return await SharedMediaManager.shared.processMediaItem(withLocalIdentifier: assetId)
            }

            // If the itemIdentifier has the "ph://" prefix
            if itemId.hasPrefix("ph://") {
                // Extract the asset ID from the itemIdentifier
                let withoutPrefix = itemId.replacingOccurrences(of: "ph://", with: "")

                let fetchResult = PHAsset.fetchAssets(withLocalIdentifiers: [withoutPrefix], options: nil)
                if fetchResult.count > 0 {
                    return await SharedMediaManager.shared.processMediaItem(withLocalIdentifier: withoutPrefix)
                }

                // Try splitting by "/" and using the first part
                let components = withoutPrefix.components(separatedBy: "/")
                if components.count > 0 {
                    let firstPart = components[0]

                    let fetchResult = PHAsset.fetchAssets(withLocalIdentifiers: [firstPart], options: nil)
                    if fetchResult.count > 0 {
                        return await SharedMediaManager.shared.processMediaItem(withLocalIdentifier: firstPart)
                    }
                }
            }
        }

        // Fallback: Try to load the item as a URL
        if let itemURL = try? await item.loadTransferable(type: URL.self) {
            // Check if this is a photos-asset URL
            if itemURL.scheme == "photos-asset" {
                // Extract the asset ID from the URL
                let components = itemURL.absoluteString.components(separatedBy: "://")
                if components.count > 1 {
                    let assetId = components[1]

                    // Verify this is a valid PHAsset
                    let fetchResult = PHAsset.fetchAssets(withLocalIdentifiers: [assetId], options: nil)
                    if fetchResult.count > 0 {
                        return await SharedMediaManager.shared.processMediaItem(withLocalIdentifier: assetId)
                    }
                }
            }
        }

        return nil
    }

    /// Fetch an asset for display using the asset identifier
    func fetchAsset(for media: JumpMediaStruct, completion: @escaping (Any?) -> Void) {
        guard let id = media.assetIdentifier else {
            completion(nil)
            return
        }

        // First try to find the asset as a shared asset
        if let asset = SharedMediaManager.shared.fetchSharedAsset(with: id) {
            completion(asset)
            return
        }

        // If not found as a shared asset, try as a local asset
        let assets = PHAsset.fetchAssets(withLocalIdentifiers: [id], options: nil)
        guard let asset = assets.firstObject else {
            print("Asset with ID \(id) is no longer available")
            completion(nil)
            return
        }

        if media.type == .photo {
            let options = PHImageRequestOptions()
            options.isNetworkAccessAllowed = true
            PHImageManager.default().requestImage(
                for: asset,
                targetSize: PHImageManagerMaximumSize,
                contentMode: .aspectFit,
                options: options
            ) { image, _ in
                completion(image)
            }
        } else {
            let options = PHVideoRequestOptions()
            options.isNetworkAccessAllowed = true
            PHImageManager.default().requestPlayerItem(
                forVideo: asset,
                options: options
            ) { playerItem, _ in
                completion(playerItem)
            }
        }
    }

    /// Determine media type from a PhotosPickerItem based on its supported content types
    private func mediaTypeFor(_ item: PhotosPickerItem) -> JumpMediaStruct.MediaType {
        // Default to photo if we can't determine the type
        // The actual type will be determined when we process the data
        return .photo
    }

    /// Determines if a URL points to a video file
    /// - Parameter url: The URL to check
    /// - Returns: True if the URL points to a video file
    func isVideoURL(_ url: URL) -> Bool {
        let videoExtensions = ["mp4", "mov", "m4v", "3gp", "avi", "mkv"]
        return videoExtensions.contains(url.pathExtension.lowercased())
    }

    /// Returns the Photos asset ID if it exists, or nil if this item came from
    /// Files (or the user took a brand-new photo/video).
    /// - Parameter item: The PhotosPickerItem to extract the identifier from
    /// - Returns: The PHAsset local identifier, or nil if not available
    private func getAssetIdentifier(from item: PhotosPickerItem) async -> String? {
        // This method is no longer used directly - we use SharedMediaManager instead
        // It's kept for compatibility with existing code
        return nil
    }

    /// Determines if a URL points to an image file
    /// - Parameter url: The URL to check
    /// - Returns: True if the URL points to an image file
    func isImageURL(_ url: URL) -> Bool {
        let imageExtensions = ["jpg", "jpeg", "png", "gif", "heic", "heif", "tiff", "tif", "bmp"]
        return imageExtensions.contains(url.pathExtension.lowercased())
    }

    // Methods for saving media to disk have been removed as we now only use asset identifiers

    /// Removes all media links from jumps
    /// - Returns: The number of links removed
    func clearAllMediaLinks() -> Int {
        // Clear all media references from jumps
        var linksRemoved = 0

        // Get all jumps from Core Data
        let context = PersistenceController.shared.container.viewContext

        // Delete all JumpMedia entities
        let mediaFetchRequest = CoreDataFetchRequestHelper.createJumpMediaFetchRequest(context: context)

        do {
            let mediaItems = try context.fetch(mediaFetchRequest)
            linksRemoved = mediaItems.count

            // Delete all media items
            for media in mediaItems {
                context.delete(media)
            }



            // Collect all sessions that need to be updated
            var sessionsToUpdate = Set<Session>()

            // Fetch all jumps to find their sessions
            let jumpFetchRequest = CoreDataFetchRequestHelper.createJumpFetchRequest(context: context)
            let jumps = try context.fetch(jumpFetchRequest)

            for jump in jumps {
                if let session = jump.session {
                    sessionsToUpdate.insert(session)
                }
            }

            // Save the context to persist changes
            try context.save()

            // Post notifications to refresh all views that display media
            DispatchQueue.main.async {
                // First post individual notifications for each affected session
                for session in sessionsToUpdate {
                    NotificationCenter.default.post(
                        name: NSNotification.Name("SessionDataChanged"),
                        object: session
                    )
                }

                // Then post the general media files changed notification
                NotificationCenter.default.post(name: NSNotification.Name("MediaFilesChanged"), object: nil)

                // Post a notification to force fetch request refresh
                NotificationCenter.default.post(name: NSNotification.Name("RefreshFetchRequests"), object: nil)
            }
        } catch {
            // Error handling is silent
        }

        return linksRemoved
    }

    /// Counts the number of media items by type (photos vs videos)
    /// - Returns: A tuple containing the count of photos and videos
    func countMediaItemsByType() -> (photos: Int, videos: Int) {
        var photos = 0
        var videos = 0

        // Get all jumps from Core Data
        let context = PersistenceController.shared.container.viewContext
        let fetchRequest = CoreDataFetchRequestHelper.createJumpFetchRequest(context: context)

        do {
            let jumps = try context.fetch(fetchRequest)

            for jump in jumps {
                let mediaItems = jump.getMediaItemsAsStructs()
                if !mediaItems.isEmpty {
                    for media in mediaItems {
                        if media.type == .photo {
                            photos += 1
                        } else if media.type == .video {
                            videos += 1
                        }
                    }
                }
            }
        } catch {
            // Error handling is silent
        }

        return (photos, videos)
    }

    /// Generates a thumbnail for a media item
    /// - Parameter media: The media item (either JumpMedia entity or JumpMediaStruct)
    /// - Returns: The thumbnail image, or nil if generation failed
    func generateThumbnail(for media: Any) async -> UIImage? {
        // Determine the type of media object
        let isJumpMediaEntity = media is JumpMedia
        let isJumpMediaStruct = media is JumpMediaStruct

        if !isJumpMediaEntity && !isJumpMediaStruct {
            return nil
        }

        // Get the asset identifier based on the object type
        let assetIdentifier: String?
        let isVideo: Bool

        if isJumpMediaEntity {
            let entity = media as! JumpMedia
            assetIdentifier = entity.assetIdentifier
            isVideo = entity.type == "video"
        } else {
            let struct_ = media as! JumpMediaStruct
            assetIdentifier = struct_.assetIdentifier
            isVideo = struct_.type == .video
        }

        // Try to use the asset identifier if available
        if let assetIdentifier = assetIdentifier {
            // First try to find the asset as a shared asset
            var asset: PHAsset?

            // Try to get the asset from the shared album
            asset = SharedMediaManager.shared.fetchSharedAsset(with: assetIdentifier)

            // If not found as a shared asset, try as a local asset
            if asset == nil {
                let fetchResult = PHAsset.fetchAssets(withLocalIdentifiers: [assetIdentifier], options: nil)
                asset = fetchResult.firstObject
            }

            if let asset = asset {
                // Request a thumbnail from the Photos library
                let options = PHImageRequestOptions()
                options.isNetworkAccessAllowed = true     // pull from iCloud if needed
                options.deliveryMode = .highQualityFormat

                // For grid thumbnails (e.g. 300×300 points)
                let thumbnail = await withCheckedContinuation { continuation in
                    SharedMediaManager.shared.cachingImageManager.requestImage(
                        for: asset,
                        targetSize: CGSize(width: 300, height: 300),
                        contentMode: .aspectFill,
                        options: options
                    ) { image, _ in
                        continuation.resume(returning: image)
                    }
                }

                if let thumbnail = thumbnail {
                    return thumbnail
                }
            }
        }

        // Create a placeholder based on the media type
        let size = CGSize(width: 300, height: 300)
        UIGraphicsBeginImageContextWithOptions(size, false, 0)

        let rect = CGRect(origin: .zero, size: size)
        UIColor.systemGray5.setFill()
        UIBezierPath(roundedRect: rect, cornerRadius: 8).fill()

        let iconName = isVideo ? "video" : "photo"
        let iconImage = UIImage(systemName: iconName)
        iconImage?.draw(in: CGRect(x: size.width/4, y: size.width/4, width: size.width/2, height: size.height/2), blendMode: .normal, alpha: 0.7)

        let placeholder = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()

        return placeholder
    }

    // Thumbnail deletion has been removed as we no longer store thumbnails locally
}
