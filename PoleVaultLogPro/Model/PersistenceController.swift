import CoreData
import CloudKit
import SwiftUI
import UIKit

/// Manages the Core Data stack for the application, handling persistence and CloudKit integration.
class PersistenceController {
    /// The shared singleton instance of the persistence controller.
    static var shared = PersistenceController()

    /// A test instance of the persistence controller with a pre-populated in-memory store.
    static var preview: PersistenceController = {
        let controller = PersistenceController(inMemory: true)

        // Create sample data for previews
        let viewContext = controller.container.viewContext

        // Sample athlete
        let athlete = Athlete(context: viewContext)
        athlete.id = UUID().uuidString
        athlete.name = "<PERSON>"
        athlete.dateOfBirth = Calendar.current.date(from: DateComponents(year: 2005, month: 5, day: 15))
        athlete.dominantHand = "right"
        athlete.personalBestCm = 450.0
        athlete.personalBestIn = HeightConverter.cmToInches(450.0)

        // Create multiple sample sessions
        let sessionTypes = ["Practice", "Meet"]
        let locations = ["Central High School", "State Championship", "Regional Meet", "Training Center"]
        let weatherConditions = ["Sunny, 75°F", "Cloudy, 68°F", "Windy, 70°F", "Clear, 72°F"]

        // Create 5 sessions with different dates
        for i in 0..<5 {
            let session = Session(context: viewContext)
            session.id = UUID().uuidString
            session.athlete = athlete

            // Create dates going back from today
            let sessionDate = Calendar.current.date(byAdding: .day, value: -i * 3, to: Date())!
            session.date = sessionDate.timeIntervalSinceReferenceDate

            // Alternate between practice and meet
            let sessionType = sessionTypes[i % 2]
            session.type = sessionType

            // Set title based on type
            if sessionType == "Practice" {
                let formatter = DateFormatter()
                formatter.dateFormat = "MMM d"
                session.title = "Practice - \(formatter.string(from: sessionDate))"
            } else {
                session.title = "Meet - \(locations[i % locations.count])"
            }

            session.location = locations[i % locations.count]
            session.weather = weatherConditions[i % weatherConditions.count]
            session.notesAthlete = "Felt good today. Working on technique."
            session.notesCoach = "Focus on takeoff position and pole bend."

            // No need to initialize grid heights - they'll be created as needed

            // Create sample jumps for each session
            var bestHeight: Double = 0
            let baseHeight = 400.0 + Double(i * 10) // Increase base height for each session

            // Add heights to grid
            let heights = [baseHeight, baseHeight + 15, baseHeight + 30, baseHeight + 45]
            for height in heights {
                _ = session.addGridHeight(heightCm: height)
            }

            // Create jumps for each height and attempt
            var order: Int16 = 1
            for height in heights {
                for attemptIndex in 1...3 {
                    // Skip some attempts to create a realistic pattern
                    if attemptIndex == 3 && height < baseHeight + 30 {
                        continue // Skip third attempts at lower heights
                    }

                    // Determine result based on height and attempt
                    let result: String
                    if height < baseHeight + 30 {
                        // Lower heights - more makes
                        result = attemptIndex == 1 ? "make" : (attemptIndex == 2 ? "make" : "miss")
                    } else if height == baseHeight + 30 {
                        // Middle height - mixed results
                        result = attemptIndex == 1 ? "miss" : (attemptIndex == 2 ? "make" : "miss")
                    } else {
                        // Highest height - mostly misses
                        result = attemptIndex == 2 ? "make" : "miss"
                    }

                    // Create the jump
                    let jump = Jump(context: viewContext)
                    jump.id = UUID().uuidString
                    jump.session = session
                    jump.order = order
                    jump.barHeightCm = height
                    jump.barHeightIn = HeightConverter.cmToInches(height)
                    jump.result = result
                    jump.resultCode = result == "make" ? "O" : (result == "miss" ? "X" : "P")
                    jump.attemptIndex = Int16(attemptIndex)

                    // Set columnIndex based on the height's position in the grid
                    if let index = heights.firstIndex(of: height) {
                        jump.columnIndex = Int16(index)
                    }

                    // Set technical fields
                    jump.runStartCm = 1000 + Double.random(in: -50...50)
                    jump.handHoldCm = 350 + Double.random(in: -20...20)
                    jump.takeOffStepCm = 300 + Double.random(in: -15...15)
                    jump.standardCm = 45 + Double.random(in: -5...5)

                    // Add comments to some jumps
                    if attemptIndex == 1 && height == baseHeight {
                        jump.comment = "Good takeoff position"
                    } else if attemptIndex == 2 && height == baseHeight + 15 {
                        jump.comment = "Perfect clearance"
                    } else if attemptIndex == 3 && height == baseHeight + 30 {
                        jump.comment = "Clipped with heel"
                    } else if attemptIndex == 2 && height == baseHeight + 45 {
                        jump.comment = "Best jump of the day"
                    }

                    // Update best height if needed
                    if result == "make" && height > bestHeight {
                        bestHeight = height
                    }

                    order += 1
                }
            }

            // Set best height for the session
            session.bestHeightCm = bestHeight
            session.bestHeightIn = HeightConverter.cmToInches(bestHeight)
        }

        try? viewContext.save()
        return controller
    }()

    /// The Core Data container for the application.
    var container: NSPersistentCloudKitContainer

    /// Initializes the persistence controller.
    /// - Parameter inMemory: Whether to use an in-memory store (for previews and testing).
    init(inMemory: Bool = false) {
        // Create the container with our model
        container = NSPersistentCloudKitContainer(name: AppConfiguration.coreDataModelName)

        // Configure the persistent store description
        if inMemory {
            // For testing and previews, use an in-memory store
            container.persistentStoreDescriptions.first?.url = URL(fileURLWithPath: "/dev/null")
        } else {
            // Configure CloudKit integration with minimal required settings
            if let description = container.persistentStoreDescriptions.first {
                // Get the CloudKit container ID from centralized configuration
                let containerIdentifier = AppConfiguration.cloudKitContainerID

                // Configure CloudKit container options
                let cloudKitOptions = NSPersistentCloudKitContainerOptions(
                    containerIdentifier: containerIdentifier
                )

                // Apply the CloudKit options to the store description
                description.cloudKitContainerOptions = cloudKitOptions

                print("CloudKit container ID set to: \(containerIdentifier)")

                // Set up notification for CloudKit account changes
                NotificationCenter.default.addObserver(
                    self,
                    selector: #selector(cloudKitAccountChanged),
                    name: .CKAccountChanged,
                    object: nil
                )

                // Set up notification for CloudKit import/export progress
                NotificationCenter.default.addObserver(
                    self,
                    selector: #selector(handleCloudKitImportProgress(_:)),
                    name: NSPersistentCloudKitContainer.eventChangedNotification,
                    object: container
                )
            }
        }

        // Configure migration options
        let migrationOptions: [String: NSNumber] = [
            NSMigratePersistentStoresAutomaticallyOption: true as NSNumber,
            NSInferMappingModelAutomaticallyOption: true as NSNumber
        ]

        // Apply migration options to all store descriptions
        for description in container.persistentStoreDescriptions {
            description.setOption(true as NSNumber, forKey: NSPersistentHistoryTrackingKey)
            description.setOption(true as NSNumber, forKey: NSPersistentStoreRemoteChangeNotificationPostOptionKey)

            // Add migration options
            for (key, value) in migrationOptions {
                description.setOption(value, forKey: key)
            }

            // Print store URL for debugging
            if let url = description.url {
                print("Persistent store URL: \(url)")
            }
        }

        // Load the persistent stores
        container.loadPersistentStores { description, error in
            if let error = error as NSError? {
                // Log the error details
                print("Error loading persistent stores: \(error), \(error.userInfo)")
                print("Detailed error info: \(error.userInfo)")

                // Check for specific error codes
                if error.code == NSPersistentStoreIncompatibleVersionHashError ||
                   error.code == NSMigrationError ||
                   error.code == NSMigrationMissingSourceModelError ||
                   error.code == NSMigrationMissingMappingModelError {
                    print("Migration error detected. Setting flag for user-initiated reset.")
                    // Instead of automatically resetting, set a flag for user-initiated reset
                    UserDefaults.standard.set(true, forKey: "needsCoreDataReset")
                } else {
                    print("Core Data error detected. Setting flag for user-initiated reset.")
                    // Instead of automatically resetting, set a flag for user-initiated reset
                    UserDefaults.standard.set(true, forKey: "needsCoreDataReset")
                }

                // Post notification about Core Data load error
                DispatchQueue.main.async {
                    NotificationCenter.default.post(
                        name: NSNotification.Name("CoreDataLoadError"),
                        object: nil,
                        userInfo: ["error": error]
                    )
                }
            } else {
                print("Core Data is initialized and ready")

                // Run migrations if needed
                DispatchQueue.main.async {
                    CoreDataMigrationManager.shared.performMigrations(in: self.container.viewContext)
                }
            }
        }

        // Configure automatic merging of changes from the parent context
        container.viewContext.automaticallyMergesChangesFromParent = true

        // Configure merge policy to handle conflicts
        container.viewContext.mergePolicy = NSMergeByPropertyObjectTrumpMergePolicy
    }



    /// Handles CloudKit import progress notifications.
    @objc private func handleCloudKitImportProgress(_ notification: Notification) {
        guard let eventNotification = notification.userInfo?[NSPersistentCloudKitContainer.eventNotificationUserInfoKey] as? NSPersistentCloudKitContainer.Event else {
            return
        }

        switch eventNotification.type {
        case .setup:
            print("CloudKit setup event: \(eventNotification)")
        case .import:
            print("CloudKit import event: \(eventNotification)")
        case .export:
            print("CloudKit export event: \(eventNotification)")
        @unknown default:
            print("Unknown CloudKit event: \(eventNotification)")
        }
    }

    /// Saves changes to the view context if there are changes to save.
    /// - Throws: An error if the save operation fails.
    func save() throws {
        let context = container.viewContext
        if context.hasChanges {
            try context.save()
        }
    }

    /// Creates a new background context for performing operations off the main thread.
    /// - Returns: A new background context.
    func newBackgroundContext() -> NSManagedObjectContext {
        return container.newBackgroundContext()
    }

    /// Minimal method to save context and let NSPersistentCloudKitContainer handle sync
    /// This is kept for backward compatibility with existing code
    func triggerCloudKitSync(completion: ((Bool, Error?) -> Void)? = nil) {
        print("🔄 Letting NSPersistentCloudKitContainer handle CloudKit sync")

        // Simply save any pending changes to trigger the built-in sync mechanism
        let context = self.container.viewContext

        context.perform {
            do {
                // Save any pending changes
                if context.hasChanges {
                    try context.save()
                    print("💾 Saved pending changes to trigger CloudKit sync")
                } else {
                    print("ℹ️ No changes to save, letting system handle sync timing")
                }

                // Complete successfully
                DispatchQueue.main.async {
                    completion?(true, nil)
                }
            } catch {
                print("❌ Error during sync: \(error.localizedDescription)")
                completion?(false, error)
            }
        }
    }

    /// Manually triggers a CloudKit sync operation
    /// This method can be called when the user explicitly requests a sync
    /// This simplified version just saves the context and lets NSPersistentCloudKitContainer handle the sync
    /// - Returns: A tuple containing success status and any error that occurred
    func manualCloudKitSync() async -> (success: Bool, error: Error?) {
        #if DEBUG
        print("🔄 Manual CloudKit sync initiated - letting NSPersistentCloudKitContainer handle it")
        #endif

        do {
            // Simply save any pending changes to trigger the built-in sync mechanism
            if container.viewContext.hasChanges {
                try container.viewContext.save()
                print("💾 Saved pending changes to trigger CloudKit sync")
            } else {
                print("ℹ️ No changes to save, letting system handle sync timing")
            }

            // Notify that a manual sync was completed
            DispatchQueue.main.async {
                NotificationCenter.default.post(
                    name: NSNotification.Name("ManualCloudKitSyncCompleted"),
                    object: nil
                )
            }

            return (true, nil)
        } catch {
            #if DEBUG
            print("❌ Error during manual CloudKit sync: \(error.localizedDescription)")
            #endif

            // Notify about the sync error
            DispatchQueue.main.async {
                NotificationCenter.default.post(
                    name: NSNotification.Name("ManualCloudKitSyncFailed"),
                    object: nil,
                    userInfo: ["error": error]
                )
            }

            return (false, error)
        }
    }

    // The forceCloudKitImport method has been removed to follow CloudKit best practices
    // Let NSPersistentCloudKitContainer handle the sync process automatically

    /// Runs CloudKit diagnostics to check for cloud-only records
    private func runCloudKitDiagnostics() async -> (hasCloudOnlyRecords: Bool, entityTypes: [String]) {
        // Simplified diagnostic check - just return false since we removed the diagnostic manager
        #if DEBUG
        print("📊 CloudKit diagnostics disabled - returning no cloud-only records")
        #endif

        return (false, [])
    }

    /// Handles CloudKit account changes
    @objc private func cloudKitAccountChanged() {
        print("CloudKit account changed - checking new status")

        // Get the CloudKit container ID from centralized configuration
        let containerIdentifier = AppConfiguration.cloudKitContainerID
        DispatchQueue.main.async {
            self.checkCloudKitStatus(containerIdentifier)
        }
    }

    /// Checks the current CloudKit status
    private func checkCloudKitStatus(_ containerIdentifier: String) {
        CKContainer(identifier: containerIdentifier).accountStatus { status, error in
            if let error = error {
                print("CloudKit account status error: \(error.localizedDescription)")
            } else {
                let statusMessage: String
                switch status {
                case .available:
                    statusMessage = "CloudKit is available and will be used for syncing"
                    // Verify container configuration
                    self.verifyCloudKitContainer(containerIdentifier)
                case .noAccount:
                    statusMessage = "No iCloud account is available. Data will be stored locally only"
                case .restricted:
                    statusMessage = "iCloud access is restricted. Data will be stored locally only"
                case .couldNotDetermine:
                    statusMessage = "Could not determine iCloud account status. Data will be stored locally only"
                case .temporarilyUnavailable:
                    statusMessage = "iCloud is temporarily unavailable. Data will be stored locally only"
                @unknown default:
                    statusMessage = "Unknown iCloud account status. Data will be stored locally only"
                }

                print("CloudKit status: \(statusMessage)")

                // Post notification about CloudKit status
                DispatchQueue.main.async {
                    NotificationCenter.default.post(name: NSNotification.Name("CloudKitStatusChanged"), object: nil)
                }
            }
        }
    }

    /// Verifies that the CloudKit container is properly configured
    private func verifyCloudKitContainer(_ containerIdentifier: String) {
        let container = CKContainer(identifier: containerIdentifier)

        // Try to fetch the user record to verify container access
        container.fetchUserRecordID { recordID, error in
            if let error = error as? CKError {
                if error.code == .badContainer {
                    print("Error fetching user record ID: Bad Container error - \(error.localizedDescription)")
                    print("The CloudKit container '\(containerIdentifier)' needs to be configured in your Apple Developer account")

                    // Continue with local storage only
                    print("Continuing with local storage only")
                } else {
                    print("Error fetching user record ID: \(error.localizedDescription)")
                }

                // Post notification about CloudKit status
                DispatchQueue.main.async {
                    NotificationCenter.default.post(name: NSNotification.Name("CloudKitStatusChanged"), object: nil)
                }
            } else if recordID != nil {
                print("Successfully verified CloudKit container configuration with user record ID: \(recordID!.recordName)")

                // Post notification about CloudKit status
                DispatchQueue.main.async {
                    NotificationCenter.default.post(name: NSNotification.Name("CloudKitStatusChanged"), object: nil)
                }
            }
        }
    }



    /// Resets all data by deleting and recreating the persistent store
    func resetAllData() {
        guard let storeURL = container.persistentStoreDescriptions.first?.url else {
            print("Failed to get store URL")
            return
        }

        print("Resetting Core Data store at: \(storeURL)")

        // Remove all stores from the coordinator
        for store in container.persistentStoreCoordinator.persistentStores {
            do {
                try container.persistentStoreCoordinator.remove(store)
                print("Successfully removed store from coordinator")
            } catch {
                print("Failed to remove store: \(error)")
            }
        }

        // Delete the store files
        let fileManager = FileManager.default
        _ = storeURL.path
        let storeDirectoryURL = storeURL.deletingLastPathComponent()

        do {
            // Get all files in the store directory
            let storeFiles = try fileManager.contentsOfDirectory(atPath: storeDirectoryURL.path)
            print("Found \(storeFiles.count) files in store directory")

            // Delete all files related to the store
            var deletedFiles = 0
            for file in storeFiles {
                if file.contains("VaultLog") || file.contains("cloudkit") || file.contains(".sqlite") {
                    let fileURL = storeDirectoryURL.appendingPathComponent(file)
                    try fileManager.removeItem(at: fileURL)
                    print("Deleted store file: \(file)")
                    deletedFiles += 1
                }
            }
            print("Deleted \(deletedFiles) store-related files")

            // Configure migration options for the new store
            let options: [String: NSNumber] = [
                NSMigratePersistentStoresAutomaticallyOption: true as NSNumber,
                NSInferMappingModelAutomaticallyOption: true as NSNumber
            ]

            // Recreate the store with migration options
            try container.persistentStoreCoordinator.addPersistentStore(
                ofType: NSSQLiteStoreType,
                configurationName: nil,
                at: storeURL,
                options: options
            )

            print("Successfully reset all data and recreated store")

            // Notify the app that data has been reset
            DispatchQueue.main.async {
                NotificationCenter.default.post(name: NSNotification.Name("DataReset"), object: nil)
            }
        } catch {
            print("Failed to reset data: \(error)")
        }
    }

    /// Reinitializes the Core Data stack after a reset
    func reinitialize() {
        print("Reinitializing Core Data stack")

        // First, clear any cached model information
        NSManagedObjectContext.mergeChanges(fromRemoteContextSave: [:], into: [])

        // Reset the view context to release all objects
        container.viewContext.reset()

        // Remove all stores from the coordinator to ensure a clean slate
        let coordinator = container.persistentStoreCoordinator
        for store in coordinator.persistentStores {
            do {
                try coordinator.remove(store)
                print("Successfully removed store from coordinator")
            } catch {
                print("Failed to remove store: \(error)")
            }
        }

        // Create a new container with a fresh model
        let newContainer = NSPersistentCloudKitContainer(name: AppConfiguration.coreDataModelName)

        // Configure the persistent store description with minimal required settings
        if let description = newContainer.persistentStoreDescriptions.first {
            // Get the CloudKit container ID from centralized configuration
            let containerIdentifier = AppConfiguration.cloudKitContainerID
            print("Using CloudKit container ID: \(containerIdentifier)")

            // Configure CloudKit container options
            let cloudKitOptions = NSPersistentCloudKitContainerOptions(
                containerIdentifier: containerIdentifier
            )

            // Apply the CloudKit options to the store description
            description.cloudKitContainerOptions = cloudKitOptions

            print("CloudKit container ID set to: \(containerIdentifier)")
        }

        // Configure migration options
        let migrationOptions: [String: NSNumber] = [
            NSMigratePersistentStoresAutomaticallyOption: true as NSNumber,
            NSInferMappingModelAutomaticallyOption: true as NSNumber
        ]

        // Apply migration options to all store descriptions
        for description in newContainer.persistentStoreDescriptions {
            description.setOption(true as NSNumber, forKey: NSPersistentHistoryTrackingKey)
            description.setOption(true as NSNumber, forKey: NSPersistentStoreRemoteChangeNotificationPostOptionKey)

            // Add migration options
            for (key, value) in migrationOptions {
                description.setOption(value, forKey: key)
            }

            // Print store URL for debugging
            if let url = description.url {
                print("Persistent store URL: \(url)")
            }
        }

        // Load the persistent stores
        newContainer.loadPersistentStores { description, error in
            if let error = error as NSError? {
                // Log the error details
                print("Error loading persistent stores during reinitialization: \(error), \(error.userInfo)")

                // If there's an error, try to recover by destroying and recreating the store
                if let url = description.url {
                    do {
                        try FileManager.default.removeItem(at: url)
                        print("Removed problematic store file at \(url)")

                        // Try loading again
                        do {
                            try newContainer.persistentStoreCoordinator.addPersistentStore(
                                ofType: NSSQLiteStoreType,
                                configurationName: nil,
                                at: url,
                                options: migrationOptions as [String : Any]
                            )
                            print("Successfully recreated store at \(url)")
                        } catch {
                            print("Failed to recreate store: \(error)")
                        }
                    } catch {
                        print("Failed to remove problematic store: \(error)")
                    }
                }
            } else {
                print("Core Data store reinitialized successfully")
            }
        }

        // Configure automatic merging of changes from the parent context
        newContainer.viewContext.automaticallyMergesChangesFromParent = true

        // Configure merge policy to handle conflicts
        newContainer.viewContext.mergePolicy = NSMergeByPropertyObjectTrumpMergePolicy

        // Post notification that Core Data context is about to be replaced
        DispatchQueue.main.async {
            NotificationCenter.default.post(name: NSNotification.Name("CoreDataContextReset"), object: nil)
        }

        // Replace the old container with the new one
        container = newContainer

        // Reset the flag indicating that reinitialization is needed
        UserDefaults.standard.set(false, forKey: "needsCoreDataReinitialize")

        print("Core Data stack has been reinitialized")

        // Post notification that Core Data has been reinitialized
        DispatchQueue.main.async {
            NotificationCenter.default.post(name: NSNotification.Name("CoreDataReinitialized"), object: nil)
        }
    }
}
