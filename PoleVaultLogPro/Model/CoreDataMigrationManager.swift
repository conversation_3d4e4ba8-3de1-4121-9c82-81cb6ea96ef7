import Foundation
import CoreData

/// Manages Core Data model migrations
class CoreDataMigrationManager {
    /// Shared instance
    static let shared = CoreDataMigrationManager()
    
    /// Private initializer for singleton
    private init() {}
    
    /// Migrates existing GridHeight entities to add ID values
    /// - Parameter context: The managed object context to use
    func migrateGridHeightEntities(in context: NSManagedObjectContext) {
        // Fetch all GridHeight entities
        let fetchRequest: NSFetchRequest<GridHeight> = GridHeight.fetchRequest()
        
        do {
            let gridHeights = try context.fetch(fetchRequest)
            print("Migrating \(gridHeights.count) GridHeight entities")
            
            // Add ID to each GridHeight entity
            for gridHeight in gridHeights {
                // Skip if the entity already has an ID
                if let id = gridHeight.value(forKey: "id") as? String, !id.isEmpty {
                    continue
                }
                
                // Generate a new UUID for the ID
                gridHeight.setValue(UUID().uuidString, forKey: "id")
                
                // Set heightIn if it's not already set
                if gridHeight.value(forKey: "heightIn") == nil || (gridHeight.value(forKey: "heightIn") as? Double ?? 0) == 0 {
                    let heightCm = gridHeight.heightCm
                    let heightIn = HeightConverter.cmToInches(heightCm)
                    gridHeight.setValue(heightIn, forKey: "heightIn")
                }
            }
            
            // Save the context
            if context.hasChanges {
                try context.save()
                print("Successfully migrated GridHeight entities")
            } else {
                print("No changes to save for GridHeight entities")
            }
        } catch {
            print("Error migrating GridHeight entities: \(error)")
        }
    }
    
    /// Performs all necessary migrations
    /// - Parameter context: The managed object context to use
    func performMigrations(in context: NSManagedObjectContext) {
        // Migrate GridHeight entities
        migrateGridHeightEntities(in: context)
    }
}
