import Foundation
import CoreData

/// Helper class to create properly configured fetch requests that avoid Core Data model conflicts
class CoreDataFetchRequestHelper {

    /// Gets the entity description for a given entity name from the context's model
    /// - Parameters:
    ///   - entityName: The name of the entity
    ///   - context: The managed object context to use
    /// - Returns: The entity description, or nil if not found
    static func getEntityDescription(for entityName: String, in context: NSManagedObjectContext) -> NSEntityDescription? {
        // Get the entity description from the context's model
        guard let entityDescription = NSEntityDescription.entity(forEntityName: entityName, in: context) else {
            print("Error: Could not get entity description for \(entityName)")
            return nil
        }

        return entityDescription
    }

    /// Creates a fetch request for Session entities with explicit entity description
    /// - Parameters:
    ///   - context: The managed object context to use
    ///   - sortDescriptors: Optional sort descriptors for the fetch request
    ///   - predicate: Optional predicate for filtering results
    /// - Returns: A configured NSFetchRequest for Session entities
    static func createSessionFetchRequest(
        context: NSManagedObjectContext,
        sortDescriptors: [NSSortDescriptor]? = nil,
        predicate: NSPredicate? = nil
    ) -> NSFetchRequest<Session> {
        let fetchRequest = NSFetchRequest<Session>()

        // Get the entity description from the context's model
        if let entityDescription = getEntityDescription(for: "Session", in: context) {
            fetchRequest.entity = entityDescription
        }

        // Set sort descriptors if provided
        if let sortDescriptors = sortDescriptors {
            fetchRequest.sortDescriptors = sortDescriptors
        }

        // Set predicate if provided
        if let predicate = predicate {
            fetchRequest.predicate = predicate
        }

        return fetchRequest
    }

    /// Creates a fetch request for Jump entities with explicit entity description
    /// - Parameters:
    ///   - context: The managed object context to use
    ///   - sortDescriptors: Optional sort descriptors for the fetch request
    ///   - predicate: Optional predicate for filtering results
    /// - Returns: A configured NSFetchRequest for Jump entities
    static func createJumpFetchRequest(
        context: NSManagedObjectContext,
        sortDescriptors: [NSSortDescriptor]? = nil,
        predicate: NSPredicate? = nil
    ) -> NSFetchRequest<Jump> {
        let fetchRequest = NSFetchRequest<Jump>()

        // Get the entity description from the context's model
        if let entityDescription = getEntityDescription(for: "Jump", in: context) {
            fetchRequest.entity = entityDescription
        }

        // Set sort descriptors if provided
        if let sortDescriptors = sortDescriptors {
            fetchRequest.sortDescriptors = sortDescriptors
        }

        // Set predicate if provided
        if let predicate = predicate {
            fetchRequest.predicate = predicate
        }

        return fetchRequest
    }

    /// Creates a fetch request for Athlete entities with explicit entity description
    /// - Parameters:
    ///   - context: The managed object context to use
    ///   - sortDescriptors: Optional sort descriptors for the fetch request
    ///   - predicate: Optional predicate for filtering results
    /// - Returns: A configured NSFetchRequest for Athlete entities
    static func createAthleteFetchRequest(
        context: NSManagedObjectContext,
        sortDescriptors: [NSSortDescriptor]? = nil,
        predicate: NSPredicate? = nil
    ) -> NSFetchRequest<Athlete> {
        let fetchRequest = NSFetchRequest<Athlete>()

        // Get the entity description from the context's model
        if let entityDescription = getEntityDescription(for: "Athlete", in: context) {
            fetchRequest.entity = entityDescription
        }

        // Set sort descriptors if provided
        if let sortDescriptors = sortDescriptors {
            fetchRequest.sortDescriptors = sortDescriptors
        }

        // Set predicate if provided
        if let predicate = predicate {
            fetchRequest.predicate = predicate
        }

        return fetchRequest
    }

    /// Creates a fetch request for JumpMedia entities with explicit entity description
    /// - Parameters:
    ///   - context: The managed object context to use
    ///   - sortDescriptors: Optional sort descriptors for the fetch request
    ///   - predicate: Optional predicate for filtering results
    /// - Returns: A configured NSFetchRequest for JumpMedia entities
    static func createJumpMediaFetchRequest(
        context: NSManagedObjectContext,
        sortDescriptors: [NSSortDescriptor]? = nil,
        predicate: NSPredicate? = nil
    ) -> NSFetchRequest<JumpMedia> {
        let fetchRequest = NSFetchRequest<JumpMedia>()

        // Get the entity description from the context's model
        if let entityDescription = getEntityDescription(for: "JumpMedia", in: context) {
            fetchRequest.entity = entityDescription
        }

        // Set sort descriptors if provided
        if let sortDescriptors = sortDescriptors {
            fetchRequest.sortDescriptors = sortDescriptors
        }

        // Set predicate if provided
        if let predicate = predicate {
            fetchRequest.predicate = predicate
        }

        return fetchRequest
    }

    /// Creates a fetch request for Pole entities with explicit entity description
    /// - Parameters:
    ///   - context: The managed object context to use
    ///   - sortDescriptors: Optional sort descriptors for the fetch request
    ///   - predicate: Optional predicate for filtering results
    /// - Returns: A configured NSFetchRequest for Pole entities
    static func createPoleFetchRequest(
        context: NSManagedObjectContext,
        sortDescriptors: [NSSortDescriptor]? = nil,
        predicate: NSPredicate? = nil
    ) -> NSFetchRequest<Pole> {
        let fetchRequest = NSFetchRequest<Pole>()

        // Get the entity description from the context's model
        if let entityDescription = getEntityDescription(for: "Pole", in: context) {
            fetchRequest.entity = entityDescription
        }

        // Set sort descriptors if provided
        if let sortDescriptors = sortDescriptors {
            fetchRequest.sortDescriptors = sortDescriptors
        }

        // Set predicate if provided
        if let predicate = predicate {
            fetchRequest.predicate = predicate
        }

        return fetchRequest
    }

    /// Creates a generic fetch request with explicit entity description
    /// - Parameters:
    ///   - entityName: The name of the entity
    ///   - context: The managed object context to use
    ///   - sortDescriptors: Optional sort descriptors for the fetch request
    ///   - predicate: Optional predicate for filtering results
    /// - Returns: A configured NSFetchRequest for the specified entity
    static func createGenericFetchRequest(
        entityName: String,
        context: NSManagedObjectContext,
        sortDescriptors: [NSSortDescriptor]? = nil,
        predicate: NSPredicate? = nil
    ) -> NSFetchRequest<NSFetchRequestResult> {
        let fetchRequest = NSFetchRequest<NSFetchRequestResult>(entityName: entityName)

        // Get the entity description from the context's model
        if let entityDescription = getEntityDescription(for: entityName, in: context) {
            fetchRequest.entity = entityDescription
        }

        // Set sort descriptors if provided
        if let sortDescriptors = sortDescriptors {
            fetchRequest.sortDescriptors = sortDescriptors
        }

        // Set predicate if provided
        if let predicate = predicate {
            fetchRequest.predicate = predicate
        }

        return fetchRequest
    }
}
