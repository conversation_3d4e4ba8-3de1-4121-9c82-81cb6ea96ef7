import Foundation
import CoreData

// MARK: - Athlete Extensions

extension Athlete {
    /// Creates a new athlete with the specified properties.
    /// - Parameters:
    ///   - context: The managed object context to create the athlete in.
    ///   - name: The name of the athlete.
    ///   - dateOfBirth: The date of birth of the athlete (optional).
    ///   - dominantHand: The dominant hand of the athlete (optional).
    /// - Returns: A new athlete instance.
    static func create(in context: NSManagedObjectContext,
                      name: String,
                      dateOfBirth: Date? = nil,
                      dominantHand: String? = nil) -> Athlete {
        // Use the fetch request helper to get the entity description
        let fetchRequest = CoreDataFetchRequestHelper.createAthleteFetchRequest(context: context)
        let entityDescription = fetchRequest.entity!

        // Create a new Athlete with the explicit entity description
        let athlete = Athlete(entity: entityDescription, insertInto: context)
        athlete.id = UUID().uuidString // Use UUID string as ID
        athlete.name = name
        athlete.dateOfBirth = dateOfBirth
        athlete.dominantHand = dominantHand
        athlete.personalBestCm = 0
        athlete.personalBestIn = 0
        return athlete
    }

    /// Updates the personal best height if the provided height is greater than the current best.
    /// - Parameter heightCm: The height in centimeters to compare against the current personal best.
    /// - Returns: True if a new personal best was set, false otherwise.
    func updatePersonalBest(heightCm: Double) -> Bool {
        if heightCm > personalBestCm {
            personalBestCm = heightCm
            personalBestIn = heightCm / 2.54
            return true
        }
        return false
    }
}

// MARK: - Session Extensions

extension Session {
    /// Checks if this session has any media attachments (photos or videos)
    /// - Returns: True if the session has any jumps with media attachments
    func hasMediaAttachments() -> Bool {
        // Check if the session has any jumps
        guard let jumps = self.jumps?.allObjects as? [Jump] else {
            return false
        }

        // Check if any jump has media attachments
        for jump in jumps {
            // Check for media items
            let mediaItems = jump.getMediaItems()
            if !mediaItems.isEmpty {
                return true
            }
        }

        return false
    }

    /// Creates a new session with the specified properties.
    /// - Parameters:
    ///   - context: The managed object context to create the session in.
    ///   - athlete: The athlete associated with the session.
    ///   - type: The type of session ("practice" or "meet").
    ///   - title: The title of the session.
    ///   - date: The date of the session.
    ///   - location: The location of the session (optional).
    ///   - weather: The weather conditions during the session (optional).
    ///   - heightStandard: The height standard to use for meets (default: "Free-Range" for practice, "NFHS" for meets).
    /// - Returns: A new session instance.
    static func create(in context: NSManagedObjectContext,
                      athlete: Athlete,
                      type: String,
                      title: String? = nil,
                      date: Date = Date(),
                      location: String? = nil,
                      weather: String? = nil,
                      heightStandard: String? = nil) -> Session {
        // Use the fetch request helper to get the entity description
        let fetchRequest = CoreDataFetchRequestHelper.createSessionFetchRequest(context: context)
        let entityDescription = fetchRequest.entity!

        // Create a new Session with the explicit entity description
        let session = Session(entity: entityDescription, insertInto: context)
        session.id = UUID().uuidString // Use UUID string as ID
        session.athlete = athlete
        session.type = type
        session.date = date.timeIntervalSinceReferenceDate // Always set date programmatically
        session.location = location
        session.weather = weather

        // Generate default title if none provided
        if let providedTitle = title {
            session.title = providedTitle
        } else {
            let dateFormatter = DateFormatter()
            dateFormatter.dateFormat = "MMM dd"
            let dateString = dateFormatter.string(from: date)

            if type == "practice" {
                session.title = "Practice - \(dateString)"
            } else {
                session.title = "Meet - \(location ?? "Event")"
            }
        }

        // No need to initialize arrays - they'll be created as needed

        // Set the height standard based on session type
        if let standard = heightStandard {
            session.heightStandard = standard
        } else {
            // Default to "Free-Range" for practice, "NFHS" for meets
            session.heightStandard = type.lowercased() == "practice" ? "Free-Range" : "NFHS"
        }

        return session
    }

    /// Updates the best height for the session if the provided height is greater than the current best.
    /// - Parameter heightCm: The height in centimeters to compare against the current best.
    /// - Returns: True if a new session best was set, false otherwise.
    func updateBestHeight(heightCm: Double) -> Bool {
        if heightCm > bestHeightCm {
            bestHeightCm = heightCm
            bestHeightIn = heightCm / 2.54

            // Also update athlete's personal best if needed
            if let athlete = self.athlete {
                return athlete.updatePersonalBest(heightCm: heightCm)
            }
            return true
        }
        return false
    }



    /// Adds a height to the grid heights collection if it doesn't already exist.
    /// - Parameter heightCm: The height in centimeters to add to the grid.
    /// - Returns: True if the height was added, false if it already existed or was invalid.
    func addGridHeight(heightCm: Double) -> Bool {
        // Validate the height - don't add heights that are 0 or very close to 0
        if heightCm < 1.0 {
            print("Attempted to add invalid height: \(heightCm)")
            return false
        }

        // Check if the height already exists
        let existingHeights = getGridHeights()
        if existingHeights.contains(heightCm) {
            return false
        }

        // Create a new GridHeight using the helper method
        let context = self.managedObjectContext!
        let gridHeight = GridHeight.create(
            in: context,
            session: self,
            heightCm: heightCm,
            order: 0 // Add at the beginning (most recent first)
        )

        // Update order of existing heights
        if let gridHeights = self.gridHeights?.allObjects as? [GridHeight] {
            for height in gridHeights {
                if height != gridHeight {
                    height.order += 1
                }
            }
        }

        // Update columnIndex values for all jumps
        updateJumpColumnIndices()

        // Save the context to ensure persistence
        do {
            try context.save()
            #if DEBUG
            print("Saved grid height \(heightCm) to persistent store")
            #endif
            return true
        } catch {
            print("Error saving grid height: \(error)")
            return false
        }
    }

    /// Gets the grid heights array.
    /// - Returns: An array of heights in centimeters, with most recent first.
    func getGridHeights() -> [Double] {
        guard let gridHeights = self.gridHeights?.allObjects as? [GridHeight] else {
            // If we don't have any grid heights, try to rebuild them from jumps
            return rebuildGridHeightsFromJumps()
        }

        // Sort by order
        let sortedHeights = gridHeights.sorted { $0.order < $1.order }

        // Extract heightCm values
        let heights = sortedHeights.map { $0.heightCm }

        // If we have heights, return them
        if !heights.isEmpty {
            return heights
        }

        // If we don't have any heights, try to rebuild them from jumps
        return rebuildGridHeightsFromJumps()
    }

    /// Rebuilds grid heights from jumps when the grid heights collection is empty.
    /// - Returns: An array of heights in centimeters.
    private func rebuildGridHeightsFromJumps() -> [Double] {
        guard let jumps = self.jumps?.allObjects as? [Jump], !jumps.isEmpty else {
            return []
        }

        // Only log this for debugging
        #if DEBUG
        print("Rebuilding grid heights from jumps")
        #endif

        // Get unique heights from jumps
        var uniqueHeights = Set<Double>()
        for jump in jumps {
            if jump.barHeightCm > 1.0 { // Only include valid heights
                uniqueHeights.insert(jump.barHeightCm)
            }
        }

        // Convert to array and sort (highest first)
        let heights = Array(uniqueHeights).sorted(by: >)

        // Create GridHeight entities for each height
        for (index, heightCm) in heights.enumerated() {
            _ = GridHeight.create(
                in: self.managedObjectContext!,
                session: self,
                heightCm: heightCm,
                order: Int16(index)
            )
        }

        // Save the context to ensure persistence
        if !heights.isEmpty {
            do {
                try self.managedObjectContext?.save()
                #if DEBUG
                print("Saved rebuilt grid heights to persistent store")
                print("Rebuilt \(heights.count) heights from jumps: \(heights)")
                #endif
            } catch {
                print("Error saving rebuilt grid heights: \(error)")
            }
        }

        return heights
    }

    /// Removes a height from the grid heights collection.
    /// - Parameter heightCm: The height in centimeters to remove.
    /// - Returns: True if the height was removed, false if it wasn't found.
    func removeGridHeight(heightCm: Double) -> Bool {
        guard let gridHeights = self.gridHeights?.allObjects as? [GridHeight] else {
            return false
        }

        // Find the GridHeight with the matching heightCm
        guard let heightToRemove = gridHeights.first(where: { abs($0.heightCm - heightCm) < 0.1 }) else {
            return false
        }

        // Get the order of the height being removed
        let orderToRemove = heightToRemove.order

        // Delete the height
        self.managedObjectContext?.delete(heightToRemove)

        // Update order of remaining heights
        for height in gridHeights {
            if height != heightToRemove && height.order > orderToRemove {
                height.order -= 1
            }
        }

        // Update columnIndex values for all jumps
        updateJumpColumnIndices()

        // Save the context
        do {
            try self.managedObjectContext?.save()
            return true
        } catch {
            print("Error removing grid height: \(error)")
            return false
        }
    }

    /// Updates the columnIndex values for all jumps in the session based on the current grid heights.
    /// This should be called whenever the grid heights array changes.
    func updateJumpColumnIndices() {
        guard let context = self.managedObjectContext else { return }

        // Create a fetch request for jumps in this session
        let fetchRequest = CoreDataFetchRequestHelper.createJumpFetchRequest(
            context: context,
            predicate: NSPredicate(format: "session == %@", self)
        )

        do {
            let jumps = try context.fetch(fetchRequest)

            // Get the current grid heights
            let heights = getGridHeights()

            // Update each jump's columnIndex based on its height's position in the grid
            for jump in jumps {
                if let index = heights.firstIndex(where: { abs($0 - jump.barHeightCm) < 0.1 }) {
                    jump.columnIndex = Int16(index)
                } else {
                    // If the height is no longer in the grid, set a default value
                    // This should not happen in normal operation
                    jump.columnIndex = -1
                }
            }
        } catch {
            print("Error fetching jumps for updateJumpColumnIndices: \(error)")
        }
    }

    /// Updates an existing height in the grid heights collection.
    /// - Parameters:
    ///   - oldHeightCm: The original height in centimeters to update.
    ///   - newHeightCm: The new height in centimeters to replace it with.
    /// - Returns: True if the height was updated, false if the original height wasn't found.
    func updateGridHeight(oldHeightCm: Double, newHeightCm: Double) -> Bool? {
        guard let gridHeights = self.gridHeights?.allObjects as? [GridHeight] else {
            return false
        }

        // Check if the new height already exists (and is different from the old height)
        let existingHeights = getGridHeights()
        if existingHeights.contains(newHeightCm) && abs(oldHeightCm - newHeightCm) > 0.1 {
            return nil // Return nil to indicate a conflict
        }

        // Find the GridHeight with the matching heightCm
        guard let heightToUpdate = gridHeights.first(where: { abs($0.heightCm - oldHeightCm) < 0.1 }) else {
            return false
        }

        // Update the height
        heightToUpdate.heightCm = newHeightCm

        // Update columnIndex values for all jumps
        updateJumpColumnIndices()

        // Save the context
        do {
            try self.managedObjectContext?.save()
            return true
        } catch {
            print("Error updating grid height: \(error)")
            return false
        }
    }
}

// MARK: - Jump Extensions

extension Jump {
    /// Creates a new jump with the specified properties.
    /// - Parameters:
    ///   - context: The managed object context to create the jump in.
    ///   - session: The session associated with the jump.
    ///   - order: The order of the jump within the session.
    ///   - heightCm: The height of the bar in centimeters.
    ///   - result: The result of the jump ("make", "miss", or "pass").
    ///   - comment: A comment about the jump (optional).
    ///   - mediaItems: Array of media items attached to the jump (optional).
    ///   - attemptIndex: The attempt index (1-3) for this height.
    ///   - runStartCm: The run start distance in centimeters.
    ///   - handHoldCm: The hand hold height in centimeters.
    ///   - takeOffStepCm: The take-off step distance in centimeters.
    ///   - standardCm: The standard setting in centimeters.
    ///   - useBar: Whether a bar (true) or bungee (false) was used for the jump (default: true).
    ///   - pole: The pole used for the jump (optional).
    ///   - columnIndex: The index of the column in the grid (optional, calculated from height if not provided).
    /// - Returns: A new jump instance.
    static func create(in context: NSManagedObjectContext,
                      session: Session,
                      order: Int16,
                      heightCm: Double,
                      result: String = "miss",
                      comment: String? = nil,
                      mediaItems: [JumpMediaStruct]? = nil,
                      attemptIndex: Int16 = 1,
                      runStartCm: Double = 0,
                      handHoldCm: Double = 0,
                      takeOffStepCm: Double = 0,
                      standardCm: Double = 0,
                      useBar: Bool = true,
                      pole: Pole? = nil,
                      columnIndex: Int16? = nil) -> Jump {
        // Use the fetch request helper to get the entity description
        let fetchRequest = CoreDataFetchRequestHelper.createJumpFetchRequest(context: context)
        let entityDescription = fetchRequest.entity!

        // Create a new Jump with the explicit entity description
        let jump = Jump(entity: entityDescription, insertInto: context)
        jump.id = UUID().uuidString // Use UUID string as ID
        jump.session = session
        jump.order = order
        jump.barHeightCm = heightCm
        jump.barHeightIn = heightCm / 2.54
        jump.result = result
        jump.comment = comment

        // Add media items if provided (using Core Data relationships)
        if let mediaItems = mediaItems {
            for media in mediaItems {
                _ = jump.addMediaStruct(media)
            }
        }

        // Set columnIndex based on the height's position in the grid
        if let providedColumnIndex = columnIndex {
            jump.columnIndex = providedColumnIndex
        } else {
            // Add the height to the grid if it doesn't exist
            _ = session.addGridHeight(heightCm: heightCm)

            // Find the index of this height in the grid
            let heights = session.getGridHeights()
            if let index = heights.firstIndex(of: heightCm) {
                jump.columnIndex = Int16(index)
            } else {
                // Fallback - should never happen since we just added it
                jump.columnIndex = 0
            }
        }

        // Set the new technical fields
        jump.attemptIndex = attemptIndex
        jump.resultCode = resultCodeFromResult(result)
        jump.runStartCm = runStartCm
        jump.handHoldCm = handHoldCm
        jump.takeOffStepCm = takeOffStepCm
        jump.standardCm = standardCm
        jump.useBar = useBar
        jump.pole = pole

        // If this is a successful jump, update the session's best height
        if result == "make" {
            _ = session.updateBestHeight(heightCm: heightCm)
        }

        return jump
    }

    /// Gets the media items attached to this jump
    /// - Returns: An array of JumpMediaStruct items
    func getMediaItems() -> [JumpMediaStruct] {
        return getMediaItemsAsStructs()
    }


    /// Validates and repairs media items if needed
    /// - Returns: A tuple containing the validated media items and a boolean indicating if any repairs were made
    func validateMediaItems() -> (mediaItems: [JumpMediaStruct], needsUpdate: Bool) {
        let mediaItems = getMediaItems()
        var validMediaItems = [JumpMediaStruct]()
        var needsUpdate = false

        // Skip if no media items
        if mediaItems.isEmpty {
            return ([], false)
        }

        // Check each media item
        for media in mediaItems {
            let (isAvailable, repairedMedia) = media.validateMedia()

            if isAvailable {
                if let repairedMedia = repairedMedia {
                    // Media is available but needed repair
                    print("Repairing media item: \(media.id)")
                    validMediaItems.append(repairedMedia)
                    needsUpdate = true
                } else {
                    // Media is available and doesn't need repair
                    validMediaItems.append(media)
                }
            } else {
                // Media is not available and can't be repaired
                print("Media item not available: \(media.id)")
                needsUpdate = true
            }
        }

        return (validMediaItems, needsUpdate)
    }

    /// Updates the media items with validated ones if needed
    /// - Returns: True if any updates were made
    @discardableResult
    func updateMediaItems() -> Bool {
        let (validMediaItems, needsUpdate) = validateMediaItems()

        // If any repairs were made, update the media items
        if needsUpdate {
            // We no longer use mediaData, instead we use Core Data relationships
            // Remove existing media items
            if let mediaItems = self.mediaItems as? Set<JumpMedia> {
                for media in mediaItems {
                    self.managedObjectContext?.delete(media)
                }
            }

            // Add the valid media items
            for media in validMediaItems {
                _ = addMediaStruct(media)
            }

            print("Updated jump with validated media items")
            return true
        }

        return false
    }

    /// Adds a media item to this jump (legacy method name for compatibility)
    /// - Parameter media: The JumpMediaStruct item to add
    /// - Returns: True if the media was added successfully
    @discardableResult
    func addMedia(_ media: JumpMediaStruct) -> Bool {
        // Use the Core Data relationship method
        return addMediaStruct(media)
    }

    /// Removes a media item from this jump
    /// - Parameter mediaId: The UUID of the media item to remove
    /// - Returns: True if the media was removed successfully
    @discardableResult
    func removeMedia(withId mediaId: UUID) -> Bool {

        // Find the JumpMedia entity with this ID
        if let mediaItems = self.mediaItems as? Set<JumpMedia> {
            // Find the media item to remove
            let mediaToRemove = mediaItems.first {
                UUID(uuidString: $0.id ?? "") == mediaId
            }

            if let mediaToRemove = mediaToRemove {
                print("🔍 DEBUG: Found media to remove: \(mediaToRemove.id ?? "unknown"), type: \(mediaToRemove.type ?? "unknown")")

                // Delete the media entity
                if let context = self.managedObjectContext {
                    context.delete(mediaToRemove)
                    print("✅ DEBUG: Deleted media entity from Core Data")

                    // Explicitly notify SwiftUI that this object has changed
                    self.objectWillChange.send()

                    // Save the context to ensure persistence
                    do {
                        try context.save()
                        print("✅ DEBUG: Core Data context saved after removing media")

                        // Post notifications to refresh all views
                        DispatchQueue.main.async {
                            // Post a notification that media files have changed
                            NotificationCenter.default.post(name: NSNotification.Name("MediaFilesChanged"), object: nil)
                            print("✅ DEBUG: Posted MediaFilesChanged notification")

                            // Post a notification to force fetch request refresh
                            NotificationCenter.default.post(name: NSNotification.Name("RefreshFetchRequests"), object: nil)
                            print("✅ DEBUG: Posted RefreshFetchRequests notification")

                            // Explicitly notify SwiftUI again after the async operation
                            self.objectWillChange.send()
                            print("✅ DEBUG: Sent objectWillChange notification after async operation")

                            // Force a UI update by posting a custom notification
                            NotificationCenter.default.post(name: NSNotification.Name("JumpMediaDeleted"), object: self.id)
                            print("✅ DEBUG: Posted JumpMediaDeleted notification with jump ID: \(self.id ?? "unknown")")
                        }
                    } catch {
                        print("❌ ERROR: Error saving context after removing media: \(error.localizedDescription)")
                        return false
                    }

                    return true
                }
            } else {
                print("❌ ERROR: Media item not found with ID: \(mediaId)")
                return false
            }
        }

        print("❌ ERROR: No media items found for this jump")
        return false
    }

    /// Converts a result string to a result code.
    /// - Parameter result: The result string ("make", "miss", or "pass").
    /// - Returns: The result code ("O", "X", or "P").
    private static func resultCodeFromResult(_ result: String) -> String {
        switch result {
        case "make":
            return "O"
        case "miss":
            return "X"
        case "pass":
            return "P"
        default:
            return "X"
        }
    }

    /// Converts a result code to a result string.
    /// - Parameter code: The result code ("O", "X", or "P").
    /// - Returns: The result string ("make", "miss", or "pass").
    private static func resultFromResultCode(_ code: String) -> String {
        switch code {
        case "O":
            return "make"
        case "X":
            return "miss"
        case "P":
            return "pass"
        default:
            return "miss"
        }
    }

    /// Toggles the result of the jump between "make", "miss", and "pass".
    /// - Returns: The new result after toggling.
    func toggleResult() -> String {
        let newResult: String

        if result == "make" {
            newResult = "miss"
        } else if result == "miss" {
            newResult = "pass"
        } else {
            // If it's a pass, cycle back to make
            newResult = "make"
        }

        result = newResult
        resultCode = Jump.resultCodeFromResult(newResult)

        // Update session best height if needed
        if newResult == "make" {
            _ = session?.updateBestHeight(heightCm: barHeightCm)
        }

        return newResult
    }

    /// Sets the result of the jump to "pass".
    func markAsPass() {
        result = "pass"
        resultCode = "P"
    }

    /// Sets the result of the jump to "make".
    func markAsMake() {
        result = "make"
        resultCode = "O"

        // Update session best height
        _ = session?.updateBestHeight(heightCm: barHeightCm)
    }

    /// Sets the result of the jump to "miss".
    func markAsMiss() {
        result = "miss"
        resultCode = "X"
    }

    /// Updates the result based on a result code.
    /// - Parameter code: The result code ("O", "X", or "P").
    func updateResultFromCode(_ code: String) {
        resultCode = code
        result = Jump.resultFromResultCode(code)

        // Update session best height if needed
        if code == "O" {
            _ = session?.updateBestHeight(heightCm: barHeightCm)
        }
    }
}


// MARK: - GridHeight Extensions

extension GridHeight {
    /// Creates a new grid height with the specified properties.
    /// - Parameters:
    ///   - context: The managed object context to create the grid height in.
    ///   - session: The session associated with the grid height.
    ///   - heightCm: The height in centimeters.
    ///   - order: The order of the height in the grid.
    /// - Returns: A new grid height instance.
    static func create(in context: NSManagedObjectContext,
                      session: Session,
                      heightCm: Double,
                      order: Int16) -> GridHeight {
        let gridHeight = GridHeight(context: context)
        gridHeight.id = UUID().uuidString
        gridHeight.heightCm = heightCm
        gridHeight.heightIn = HeightConverter.cmToInches(heightCm)
        gridHeight.order = order
        gridHeight.session = session
        return gridHeight
    }
}

// MARK: - Pole Extensions

extension Pole {
    /// Creates a new pole with the specified properties.
    /// - Parameters:
    ///   - context: The managed object context to create the pole in.
    ///   - athlete: The athlete this pole belongs to.
    ///   - name: The name of the pole.
    ///   - brand: The brand of the pole (optional).
    ///   - weight: The weight rating of the pole in pounds.
    ///   - lengthCm: The length of the pole in centimeters.
    ///   - color: The color of the pole (optional).
    ///   - order: The display order of the pole in the athlete's list.
    /// - Returns: A new pole instance.
    static func create(in context: NSManagedObjectContext,
                      athlete: Athlete,
                      name: String,
                      brand: String? = nil,
                      weight: Double,
                      lengthCm: Double,
                      color: String? = nil,
                      order: Int16 = 0) -> Pole {
        // Use the fetch request helper to get the entity description
        let fetchRequest = CoreDataFetchRequestHelper.createPoleFetchRequest(context: context)
        let entityDescription = fetchRequest.entity!

        // Create a new Pole with the explicit entity description
        let pole = Pole(entity: entityDescription, insertInto: context)
        pole.id = UUID().uuidString
        pole.athlete = athlete
        pole.name = name
        pole.brand = brand
        pole.weight = weight
        pole.lengthCm = lengthCm
        pole.lengthIn = lengthCm / 2.54
        pole.color = color
        pole.order = order

        return pole
    }

    /// Gets the formatted length of the pole in the current measurement system.
    /// - Returns: A formatted string representing the pole length.
    func getFormattedLength() -> String {
        if AppTheme.useMetricSystem {
            return "\(Int(lengthCm / 100))m \(Int(lengthCm.truncatingRemainder(dividingBy: 100)))cm"
        } else {
            let feet = Int(lengthIn / 12)
            let inches = Int(lengthIn.truncatingRemainder(dividingBy: 12))
            return "\(feet)' \(inches)\""
        }
    }

    /// Gets the formatted weight of the pole.
    /// - Returns: A formatted string representing the pole weight.
    func getFormattedWeight() -> String {
        return "\(Int(weight)) lbs"
    }

    /// Gets a display name for the pole that includes key attributes.
    /// - Returns: A formatted string for display in lists.
    func getDisplayName() -> String {
        var displayName = name ?? "Unnamed Pole"

        if let brand = brand, !brand.isEmpty {
            displayName += " (\(brand))"
        }

        // No longer adding color name since the text color will indicate the color

        displayName += " - \(getFormattedLength()), \(getFormattedWeight())"

        return displayName
    }

    /// Helper function to get a friendly color name from a hex string
    private func getFriendlyColorName(from hexColor: String) -> String {
        // Convert hex to UIColor
        let scanner = Scanner(string: hexColor.trimmingCharacters(in: CharacterSet.alphanumerics.inverted))
        var hexNumber: UInt64 = 0

        if scanner.scanHexInt64(&hexNumber) {
            let r = CGFloat((hexNumber & 0xff0000) >> 16) / 255
            let g = CGFloat((hexNumber & 0x00ff00) >> 8) / 255
            let b = CGFloat(hexNumber & 0x0000ff) / 255

            // Simple color detection based on RGB values
            if r > 0.7 && g < 0.3 && b < 0.3 {
                return "Red"
            } else if r < 0.3 && g > 0.6 && b < 0.3 {
                return "Green"
            } else if r < 0.3 && g < 0.3 && b > 0.7 {
                return "Blue"
            } else if r > 0.7 && g > 0.7 && b < 0.3 {
                return "Yellow"
            } else if r > 0.7 && g > 0.4 && g < 0.7 && b < 0.3 {
                return "Orange"
            } else if r > 0.5 && g < 0.3 && b > 0.5 {
                return "Purple"
            } else if r < 0.2 && g < 0.2 && b < 0.2 {
                return "Black"
            } else if r > 0.9 && g > 0.9 && b > 0.9 {
                return "White"
            } else if r > 0.4 && r < 0.6 && g > 0.4 && g < 0.6 && b > 0.4 && b < 0.6 {
                return "Gray"
            } else if r > 0.7 && g < 0.5 && b > 0.5 {
                return "Pink"
            }
        }

        // Default to a generic name
        return "Custom"
    }
}