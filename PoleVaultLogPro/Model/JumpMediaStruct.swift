import Foundation
import Photos
import PhotosUI
import UIKit

/// A struct representing a media item attached to a jump
struct JumpMediaStruct: Identifiable, Codable, Equatable {
    /// The type of media
    enum MediaType: String, Codable {
        case photo
        case video
    }

    /// The unique identifier for this media item
    var id: UUID

    /// The type of media (photo or video)
    var type: MediaType

    /// The asset identifier in the Photos library
    /// This can be either a local PHAsset identifier or a shared album identifier
    var assetIdentifier: String?

    // We no longer support local file URLs, only asset identifiers

    /// The poster time for videos (as a percentage of the video duration)
    var posterTime: Double?

    /// Creates a new JumpMediaStruct with a random UUID
    /// - Parameters:
    ///   - type: The type of media (photo or video)
    ///   - assetIdentifier: The asset identifier in the Photos library
    ///   - posterTime: The poster time for videos (as a percentage of the video duration)
    init(type: MediaType, assetIdentifier: String? = nil, posterTime: Double? = nil) {
        self.id = UUID()
        self.type = type
        self.assetIdentifier = assetIdentifier
        self.posterTime = posterTime
    }

    /// Creates a new JumpMediaStruct with a specific UUID
    /// - Parameters:
    ///   - id: The unique identifier for this media item
    ///   - type: The type of media (photo or video)
    ///   - assetIdentifier: The asset identifier in the Photos library
    ///   - posterTime: The poster time for videos (as a percentage of the video duration)
    init(id: UUID, type: MediaType, assetIdentifier: String? = nil, posterTime: Double? = nil) {
        self.id = id
        self.type = type
        self.assetIdentifier = assetIdentifier
        self.posterTime = posterTime
    }

    /// Validates this media item to ensure it's still available
    /// - Returns: A tuple containing a boolean indicating if the media is available, and an optional repaired media item
    func validateMedia() -> (isAvailable: Bool, repairedMedia: JumpMediaStruct?) {
        // If we have an asset identifier, check if it's still valid
        if let assetId = assetIdentifier {
            let fetchResult = PHAsset.fetchAssets(withLocalIdentifiers: [assetId], options: nil)
            if fetchResult.count > 0 {
                // Asset is still valid
                return (true, nil)
            } else {
                // Asset is no longer valid
                print("Asset with ID \(assetId) is no longer available")
                return (false, nil)
            }
        }

        // If we don't have an asset identifier, the media is not available
        return (false, nil)
    }

    /// Checks if this media item is still available
    /// - Returns: A boolean indicating if the media is available
    func isAvailable() -> Bool {
        // If we have an asset identifier, check if it's still valid
        if let assetId = assetIdentifier {
            // First try to find it as a shared asset
            if SharedMediaManager.shared.fetchSharedAsset(with: assetId) != nil {
                return true
            }

            // If not found as a shared asset, try as a local asset
            let fetchResult = PHAsset.fetchAssets(withLocalIdentifiers: [assetId], options: nil)
            return fetchResult.count > 0
        }

        // If we don't have an asset identifier, the media is not available
        return false
    }

    /// This method is kept for compatibility but we no longer save thumbnails to disk
    /// - Returns: A dummy URL that won't be used
    func thumbnailURL() -> URL {
        // Return a dummy URL in the temporary directory
        return FileManager.default.temporaryDirectory.appendingPathComponent("\(id).jpg")
    }
}
