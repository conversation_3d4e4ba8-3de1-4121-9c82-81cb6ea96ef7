import Foundation
import CoreData

// MARK: - Athlete Fetch Extensions
extension Athlete {
    /// Creates a fetch request for Athlete entities
    /// - Parameter context: The managed object context to use
    /// - Returns: A configured NSFetchRequest for Athlete entities
    static func fetchRequest(in context: NSManagedObjectContext) -> NSFetchRequest<Athlete> {
        return CoreDataFetchRequestHelper.createAthleteFetchRequest(context: context)
    }
    
    /// Fetches all athletes from the database with optional sorting
    /// - Parameters:
    ///   - context: The managed object context to use
    ///   - sortDescriptors: Optional sort descriptors to apply
    /// - Returns: Array of athletes
    static func fetch(
        in context: NSManagedObjectContext,
        sortDescriptors: [NSSortDescriptor]? = nil
    ) -> [Athlete] {
        let request = fetchRequest(in: context)
        request.sortDescriptors = sortDescriptors
        
        do {
            return try context.fetch(request)
        } catch {
            print("Error fetching athletes: \(error)")
            return []
        }
    }
    
    /// Fetches a single athlete by ID
    /// - Parameters:
    ///   - id: The ID of the athlete to fetch
    ///   - context: The managed object context to use
    /// - Returns: The athlete with the specified ID, or nil if not found
    static func fetch(id: String, in context: NSManagedObjectContext) -> Athlete? {
        let request = fetchRequest(in: context)
        request.predicate = NSPredicate(format: "id == %@", id)
        request.fetchLimit = 1
        
        do {
            let results = try context.fetch(request)
            return results.first
        } catch {
            print("Error fetching athlete by ID: \(error)")
            return nil
        }
    }
    
    /// Gets the default athlete (first one found)
    /// - Parameter context: The managed object context to use
    /// - Returns: The default athlete, or nil if none exists
    static func getDefault(in context: NSManagedObjectContext) -> Athlete? {
        let request = fetchRequest(in: context)
        request.fetchLimit = 1
        
        do {
            let results = try context.fetch(request)
            return results.first
        } catch {
            print("Error fetching default athlete: \(error)")
            return nil
        }
    }
}
