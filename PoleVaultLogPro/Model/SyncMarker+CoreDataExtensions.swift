import Foundation
import CoreData
import UIKit

// MARK: - SyncMarker Extensions
extension SyncMarker {
    /// Creates a new SyncMarker with the correct entity description
    /// - Parameter context: The managed object context to use
    /// - Returns: A new SyncMarker instance
    static func create(in context: NSManagedObjectContext) -> SyncMarker {
        let fetchRequest = NSFetchRequest<NSFetchRequestResult>(entityName: "SyncMarker")
        guard let entityDescription = fetchRequest.entity else {
            fatalError("Could not get entity description for SyncMarker")
        }

        return SyncMarker(entity: entityDescription, insertInto: context)
    }

    /// Creates a new SyncMarker with the correct entity description and all properties set
    /// - Parameters:
    ///   - context: The managed object context to use
    ///   - deviceIdentifier: The device identifier for this sync marker
    /// - Returns: A new SyncMarker instance with all properties set
    static func create(
        in context: NSManagedObjectContext,
        deviceIdentifier: String? = UIDevice.current.identifierForVendor?.uuidString
    ) -> SyncMarker {
        let syncMarker = create(in: context)
        syncMarker.id = UUID().uuidString
        syncMarker.timestamp = Date().timeIntervalSinceReferenceDate
        syncMarker.deviceIdentifier = deviceIdentifier

        return syncMarker
    }
}
