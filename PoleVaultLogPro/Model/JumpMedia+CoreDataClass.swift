import Foundation
import CoreData
import Photos

extension CodingUserInfoKey {
    static let managedObjectContext = CodingUserInfoKey(rawValue: "managedObjectContext")!
}

@objc(JumpMedia)
public class JumpMedia: NSManagedObject, Codable {
    // This is the implementation class for JumpMedia

    enum CodingKeys: String, CodingKey {
        case id
        case type
        case assetIdentifier
        case posterTime
    }

    public required convenience init(from decoder: Decoder) throws {
        guard let context = decoder.userInfo[CodingUserInfoKey.managedObjectContext] as? NSManagedObjectContext else {
            throw DecodingError.dataCorruptedError(forKey: CodingKeys.id,
                                                  in: try decoder.container(keyedBy: CodingKeys.self),
                                                  debugDescription: "No managed object context provided")
        }

        self.init(context: context)

        let container = try decoder.container(keyedBy: CodingKeys.self)
        self.id = try container.decode(String.self, forKey: .id)
        self.type = try container.decode(String.self, forKey: .type)
        self.assetIdentifier = try container.decodeIfPresent(String.self, forKey: .assetIdentifier)
        self.posterTime = try container.decodeIfPresent(Double.self, forKey: .posterTime) ?? 0.0
    }

    public func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(type, forKey: .type)
        try container.encodeIfPresent(assetIdentifier, forKey: .assetIdentifier)
        try container.encodeIfPresent(posterTime, forKey: .posterTime)
    }
}
