import Foundation
import CoreData
import Photos
import PhotosUI

// Extension to JumpMedia for additional functionality
extension JumpMedia {
    /// Creates a new JumpMedia entity with the specified properties.
    /// - Parameters:
    ///   - context: The managed object context to create the JumpMedia in.
    ///   - jump: The jump associated with the media.
    ///   - type: The type of media ("photo" or "video").
    ///   - assetIdentifier: The PHAsset local identifier (optional).
    ///   - posterTime: The poster time for video thumbnails (optional).
    /// - Returns: A new JumpMedia instance.
    static func create(in context: NSManagedObjectContext,
                      jump: Jump,
                      type: String,
                      assetIdentifier: String? = nil,
                      posterTime: Double? = nil) -> JumpMedia {
        // Use the fetch request helper to get the entity description
        let fetchRequest = CoreDataFetchRequestHelper.createJumpMediaFetchRequest(context: context)
        let entityDescription = fetchRequest.entity!

        // Create a new JumpMedia with the explicit entity description
        let media = JumpMedia(entity: entityDescription, insertInto: context)
        media.id = UUID().uuidString
        media.jump = jump
        media.type = type
        media.assetIdentifier = assetIdentifier
        media.posterTime = posterTime ?? (type == "video" ? 0.5 : 0.0)

        return media
    }

    // thumbnailURL method has been removed as we no longer store thumbnails locally

    /// Checks if the media is available in the Photos library
    /// - Returns: A boolean indicating if the media is available
    func isAvailable() -> Bool {
        // First check if we have an asset identifier
        if let assetIdentifier = self.assetIdentifier {
            // First try to find it as a shared asset
            if SharedMediaManager.shared.fetchSharedAsset(with: assetIdentifier) != nil {
                return true
            }

            // If not found as a shared asset, try as a local asset
            let fetchResult = PHAsset.fetchAssets(withLocalIdentifiers: [assetIdentifier], options: nil)
            if fetchResult.count > 0 {
                // The asset exists, so the media is available
                return true
            } else {
                print("Asset with ID \(assetIdentifier) is no longer available")
                return false
            }
        }

        // If we don't have an asset identifier, the media is not available
        return false
    }
}
