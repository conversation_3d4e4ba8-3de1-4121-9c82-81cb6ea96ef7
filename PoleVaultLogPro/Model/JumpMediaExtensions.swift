import Foundation
import CoreData
import Photos

// MARK: - Jump Media Extensions

extension Jump {
    /// Gets the media items attached to this jump as JumpMediaStruct objects
    /// - Returns: An array of JumpMediaStruct items
    func getMediaItemsAsStructs() -> [JumpMediaStruct] {
        var result: [JumpMediaStruct] = []

        // Get media items from Core Data relationships
        if let mediaItems = self.mediaItems as? Set<JumpMedia>, !mediaItems.isEmpty {
            // Convert Core Data entities to JumpMediaStruct objects
            let structs = mediaItems.map { entity in
                return JumpMediaStruct(
                    id: UUID(uuidString: entity.id ?? "") ?? UUID(),
                    type: JumpMediaStruct.MediaType(rawValue: entity.type ?? "photo") ?? .photo,
                    assetIdentifier: entity.assetIdentifier,
                    posterTime: entity.posterTime
                )
            }
            result.append(contentsOf: structs)
        }

        return result
    }

    /// Adds a media item to this jump
    /// - Parameter media: The JumpMediaStruct item to add
    /// - Returns: True if the media was added successfully
    @discardableResult
    func addMediaStruct(_ media: JumpMediaStruct) -> Bool {
        guard let context = self.managedObjectContext else { return false }

        // Create a new JumpMedia entity and store it
        _ = JumpMedia.create(
            in: context,
            jump: self,
            type: media.type.rawValue,
            assetIdentifier: media.assetIdentifier,
            posterTime: media.posterTime
        )

        // Save the context
        do {
            try context.save()

            // Notify SwiftUI that this object has changed
            self.objectWillChange.send()

            return true
        } catch {
            print("Error saving context after adding media: \(error)")
            return false
        }
    }
}
