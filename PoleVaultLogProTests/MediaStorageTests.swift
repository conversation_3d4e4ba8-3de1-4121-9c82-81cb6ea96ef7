import XCTest
import Photos
import PhotosUI
@testable import PoleVaultLogPro

class MediaStorageTests: XCTestCase {

    override func setUp() {
        super.setUp()
        // No need to reset any settings for tests
    }

    override func tearDown() {
        // Clean up after each test
        // Note: We no longer store files locally, so no cleanup is needed
        super.tearDown()
    }

    // We no longer test the alwaysCopyPickedMedia setting as it has been removed

    // This test is no longer valid as we don't use direct URLs anymore
    // We only use asset identifiers from the Photos library
    func testValidateMediaWithAssetIdentifier() {
        // Create a JumpMediaStruct with an asset identifier
        let media = JumpMediaStruct(
            type: .photo,
            assetIdentifier: "test-asset-id"
        )

        // Verify that the media has the correct asset identifier
        XCTAssertEqual(media.assetIdentifier, "test-asset-id")

        // Verify that the media has the correct type
        XCTAssertEqual(media.type, .photo)
    }

    // This test is no longer valid as we don't support external files anymore
    // We only use asset identifiers from the Photos library
    func testMediaStorageManagerWithURL() async throws {
        // Create a mock PhotosPickerItem that returns a photos-asset URL
        let mockItem = MockPhotosPickerItem(url: URL(string: "photos-asset://test-asset-id")!)

        // Process the mock item
        let media = await MediaStorageManager.shared.processMediaItem(mockItem)

        // The media might be nil since we can't properly mock a PHAsset in tests
        // This is just to verify the code path is executed
        if media != nil {
            // If media is created, it should have an asset identifier
            XCTAssertNotNil(media?.assetIdentifier)

            // It should not have a fileURL
            XCTAssertNil(media?.fileURL)
        }
    }

    func testMediaStorageManagerWithAssetIdentifier() async throws {
        // This test would normally test the behavior with a PHAsset identifier
        // but we can't easily mock a PHAsset in tests

        // Instead, we'll verify that our mock implementation works as expected
        let mockItem = MockPhotosPickerItemWithIdentifier(identifier: "test-asset-id")

        // Process the mock item
        let media = await MediaStorageManager.shared.processMediaItem(mockItem)

        // Verify that the media was created
        XCTAssertNotNil(media)

        // Verify that the media has the correct asset identifier
        XCTAssertEqual(media?.assetIdentifier, "test-asset-id")

        // Verify that the media doesn't have a fileURL (since we're using the asset identifier)
        XCTAssertNil(media?.fileURL)
    }
}

// Mock PhotosPickerItem for testing with URLs
class MockPhotosPickerItem: PhotosPickerItem {
    private let mockURL: URL

    init(url: URL) {
        self.mockURL = url
        super.init()
    }

    override var itemIdentifier: String? {
        // If this is a photos-asset URL, extract the asset ID
        if mockURL.scheme == "photos-asset" {
            let components = mockURL.absoluteString.components(separatedBy: "://")
            if components.count > 1 {
                return components[1]
            }
        }
        return nil
    }

    override var supportedContentTypes: [UTType] {
        return [.image, .movie]
    }

    override func loadTransferable<T>(type: T.Type) async throws -> T? where T : Transferable {
        if type == URL.self {
            return mockURL as? T
        }
        return nil
    }
}

// Mock PhotosPickerItem for testing with asset identifiers
class MockPhotosPickerItemWithIdentifier: PhotosPickerItem {
    private let mockIdentifier: String

    init(identifier: String) {
        self.mockIdentifier = identifier
        super.init()
    }

    override var itemIdentifier: String? {
        return mockIdentifier
    }

    override var supportedContentTypes: [UTType] {
        return [.image, .movie]
    }

    override func loadTransferable<T>(type: T.Type) async throws -> T? where T : Transferable {
        if type == URL.self {
            return URL(string: "photos-asset://\(mockIdentifier)") as? T
        }
        return nil
    }
}
