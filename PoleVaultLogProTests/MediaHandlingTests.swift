import XCTest
import CoreData
@testable import PoleVaultLogPro

class MediaHandlingTests: XCTestCase {

    var container: NSPersistentContainer!
    var context: NSManagedObjectContext!

    override func setUpWithError() throws {
        // Set up an in-memory Core Data stack for testing
        container = NSPersistentContainer(name: "VaultLog")
        let description = NSPersistentStoreDescription()
        description.type = NSInMemoryStoreType
        container.persistentStoreDescriptions = [description]

        container.loadPersistentStores { (description, error) in
            XCTAssertNil(error, "Failed to load persistent stores: \(String(describing: error))")
        }

        context = container.viewContext
    }

    override func tearDownWithError() throws {
        // Clean up the Core Data stack
        context = nil
        container = nil
    }

    func testJumpMediaCreation() throws {
        // Create a test jump
        let jump = Jump(context: context)
        jump.id = UUID().uuidString
        jump.barHeightCm = 400
        jump.result = "make"

        // Create a test media item
        let media = JumpMedia(
            type: .photo,
            assetIdentifier: "test-asset-id",
            fileURL: URL(string: "file:///test/path.jpg")
        )

        // Add the media to the jump
        let success = jump.addMedia(media)
        XCTAssertTrue(success, "Failed to add media to jump")

        // Save the context
        try context.save()

        // Verify the media was added
        let mediaItems = jump.getMediaItems()
        XCTAssertEqual(mediaItems.count, 1, "Jump should have 1 media item")

        // Verify the media properties
        let retrievedMedia = mediaItems.first
        XCTAssertEqual(retrievedMedia?.type, .photo, "Media type should be photo")
        XCTAssertEqual(retrievedMedia?.assetIdentifier, "test-asset-id", "Asset identifier should match")
        XCTAssertEqual(retrievedMedia?.fileURL?.absoluteString, "file:///test/path.jpg", "File URL should match")

        // No need to verify videoLocalId anymore as it's been removed
    }

    func testJumpMediaRemoval() throws {
        // Create a test jump
        let jump = Jump(context: context)
        jump.id = UUID().uuidString
        jump.barHeightCm = 400
        jump.result = "make"

        // Create a test media item
        let media = JumpMedia(
            type: .photo,
            assetIdentifier: "test-asset-id",
            fileURL: URL(string: "file:///test/path.jpg")
        )

        // Add the media to the jump
        let addSuccess = jump.addMedia(media)
        XCTAssertTrue(addSuccess, "Failed to add media to jump")

        // Save the context
        try context.save()

        // Verify the media was added
        var mediaItems = jump.getMediaItems()
        XCTAssertEqual(mediaItems.count, 1, "Jump should have 1 media item")

        // Remove the media
        let removeSuccess = jump.removeMedia(withId: media.id)
        XCTAssertTrue(removeSuccess, "Failed to remove media from jump")

        // Save the context
        try context.save()

        // Verify the media was removed
        mediaItems = jump.getMediaItems()
        XCTAssertEqual(mediaItems.count, 0, "Jump should have 0 media items")

        // No need to verify videoLocalId anymore as it's been removed
    }

    func testMultipleMediaItems() throws {
        // Create a test jump
        let jump = Jump(context: context)
        jump.id = UUID().uuidString
        jump.barHeightCm = 400
        jump.result = "make"

        // Create test media items
        let media1 = JumpMedia(
            type: .photo,
            assetIdentifier: "test-asset-id-1",
            fileURL: URL(string: "file:///test/path1.jpg")
        )

        let media2 = JumpMedia(
            type: .video,
            assetIdentifier: "test-asset-id-2",
            fileURL: URL(string: "file:///test/path2.mp4")
        )

        // Add the media to the jump
        let addSuccess1 = jump.addMedia(media1)
        XCTAssertTrue(addSuccess1, "Failed to add media1 to jump")

        let addSuccess2 = jump.addMedia(media2)
        XCTAssertTrue(addSuccess2, "Failed to add media2 to jump")

        // Save the context
        try context.save()

        // Verify the media was added
        let mediaItems = jump.getMediaItems()
        XCTAssertEqual(mediaItems.count, 2, "Jump should have 2 media items")

        // No need to verify videoLocalId anymore as it's been removed
    }

    // Legacy videoLocalId conversion test removed as videoLocalId field has been removed
}
